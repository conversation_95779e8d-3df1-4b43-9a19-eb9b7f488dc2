import request from '@/config/axios'

export interface UserVO {
  id: number
  username: string
  nickname: string
  deptId: number
  postIds: string[]
  email: string
  mobile: string
  sex: number
  avatar: string
  loginIp: string
  status: number
  remark: string
  loginDate: Date
  created: Date
}

// 查询用户管理列表
export const getUserPage = (data) => {
  return request.post({ url: '/v1/platform/infra/account/page', data })
}

// 查询用户详情
export const getUser = (id: number) => {
  return request.get({ url: '/v1/platform/infra/account/get?id=' + id })
}

// 新增用户
export const createUser = (data: UserVO) => {
  return request.post({ url: '/v1/platform/infra/account/create', data })
}

// 修改用户
export const updateUser = (data: UserVO) => {
  return request.put({ url: '/v1/platform/infra/account/update', data })
}

// 删除用户
export const deleteUser = (id: number) => {
  return request.delete({ url: '/v1/platform/infra/account/delete?id=' + id })
}

// 导出用户
export const exportUser = (params: any) => {
  return request.download({ url: '/v1/platform/infra/account/export', params })
}

// 下载用户导入模板
export const importUserTemplate = () => {
  return request.download({ url: '/v1/platform/infra/account/get-import-template' })
}

// 用户密码重置
export const resetUserPassword = (id: number, password: string) => {
  const data = {
    id,
    password
  }
  return request.put({ url: '/v1/platform/infra/account/update-password', data: data })
}

// 用户状态修改
export const updateUserStatus = (id: number, status: number) => {
  const data = {
    id,
    status
  }
  return request.put({ url: '/v1/platform/infra/account/update-status', data: data })
}

// 获取用户精简信息列表
export const getSimpleUserList = (): Promise<UserVO[]> => {
  return request.get({ url: '/v1/platform/infra/account/simple-list' })
}
