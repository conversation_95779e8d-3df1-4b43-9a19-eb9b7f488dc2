<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="时间段名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入时间段名称" />
      </el-form-item>
      <el-form-item label="时间段编号" prop="code">
        <el-input
          v-model="formData.code"
          :disabled="formData.activityTimeSlotId"
          placeholder="请输入时间段编号"
        />
      </el-form-item>
      <el-form-item label="每日开始时间" prop="dailyStartTime">
        <el-time-picker
          v-model="formData.dailyStartTime"
          value-format="HH:mm:ss"
          placeholder="选择每日开始时间"
        />
      </el-form-item>
      <el-form-item label="每日结束时间" prop="dailyEndTime">
        <el-time-picker
          v-model="formData.dailyEndTime"
          value-format="HH:mm:ss"
          placeholder="选择每日结束时间"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-radio-group v-model="formData.enabled">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
            >{{ dict.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as SeckillActivityApi from '@/api/activity/seckillActivity'

/** 秒杀时段 表单 */
defineOptions({ name: 'TimeRangeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const route = useRoute() // 路由

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  activityTimeSlotId: undefined,
  activityId: route.params.id,
  code: undefined,
  name: undefined,
  dailyStartTime: undefined,
  dailyEndTime: undefined,
  enabled: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '时间段名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '时间段编号不能为空', trigger: 'blur' }],
  dailyStartTime: [{ required: true, message: '每日开始时间不能为空', trigger: 'blur' }],
  dailyEndTime: [{ required: true, message: '每日结束时间不能为空', trigger: 'blur' }],
  shelfStatus: [{ required: true, message: '启用状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SeckillActivityApi.getTimeslotDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      activityTimeSlotId: formData.value.activityTimeSlotId,
      activityId: formData.value.activityId,
      name: formData.value.name,
      dailyStartTime: formData.value.dailyStartTime,
      dailyEndTime: formData.value.dailyEndTime,
      enabled: formData.value.enabled
    }
    if (formType.value === 'create') {
      data.code = formData.value.code
      await SeckillActivityApi.createTimeslot(data)
      message.success(t('common.createSuccess'))
    } else {
      await SeckillActivityApi.updateTimeslot(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    activityTimeSlotId: undefined,
    activityId: route.params.id,
    code: undefined,
    name: undefined,
    dailyStartTime: undefined,
    dailyEndTime: undefined,
    enabled: undefined
  }
  formRef.value?.resetFields()
}
</script>
