<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="活动标题或编号" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入活动标题或编号" clearable @keyup.enter="handleQuery" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="开始时间" prop="startCreated">
        <el-date-picker
          v-model="queryParams.startCreated"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endCreated">
        <el-date-picker
          v-model="queryParams.endCreated"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['activity:setActivity:create']"><Icon icon="ep:plus" class="mr-5px" /> 新增</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true">
      <el-table-column label="活动编码" align="center" prop="code" />
      <el-table-column label="活动名称" align="center" prop="title" min-width="150" show-overflow-tooltip />
      <el-table-column label="活动状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ACTIVE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" />
      <el-table-column label="结束时间" align="center" prop="endTime" />
      <el-table-column label="推荐位置" align="center" prop="recommendPositions" w="300" show-overflow-tooltip>
        <template #default="scope">
          <span v-for="(option, index) in scope.row.recommendPositions" :key="option">
            {{ index > 0 ? '、' : '' }}{{ recommendPositionsMap[option] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="上架状态" align="center" prop="shelfStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SALE_STATUS" :value="scope.row.shelfStatus" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="created" :formatter="dateFormatter" width="180px"/>
      <el-table-column label="操作" align="center" width="250px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.activityId)" v-hasPermi="['activity:setActivity:update']">编辑</el-button>
          <router-link :to="'/activity/time-range/' + scope.row.activityId">
            <el-button link type="success" v-hasPermi="['activity:setActivity:update']">时间段</el-button>
          </router-link>
          <el-button link type="danger" @click="handleDelete(scope.row.activityId)" v-hasPermi="['activity:setActivity:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="paginationData.pageTotal"
      v-model:page="paginationData.pageNum"
      v-model:limit="paginationData.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SetActivityForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import * as SeckillActivityApi from '@/api/activity/seckillActivity'
import SetActivityForm from './SetActivityForm.vue'
import { DICT_TYPE, getOptionsKeyValue } from '@/utils/dict'

/** 秒杀时段 列表 */
defineOptions({ name: 'SeckillConfig' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const recommendPositionsMap = getOptionsKeyValue(DICT_TYPE.RECOMMENDED_LOCATION)

const loading = ref(true) // 列表的加载中
const list = ref<SeckillActivityApi.SeckillConfigVO[]>([]) // 列表的数据
const queryParams = reactive({
  keyword: undefined,
  startCreated: [],
  beginStartTime: undefined,
  endStartTime: undefined,
  endCreated: [],
  beginEndTime: undefined,
  endEndTime: undefined
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        keyword: queryParams.keyword || undefined,
        beginStartTime: queryParams.startCreated[0] || undefined,
        endStartTime: queryParams.startCreated[1] || undefined,
        beginEndTime: queryParams.endCreated[0] || undefined,
        endEndTime: queryParams.endCreated[0] || undefined
      }
    }
    const data = await SeckillActivityApi.getSeckillConfigPage(params)
    list.value = (data.pageContents || []).map(item => {
      return {
        ...item,
        recommendPositions: JSON.parse(item.recommendPositions)
      }
    })
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
// 设置商品
const setProduct = (id) => {
  console.log('setProduct', id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SeckillActivityApi.deleteSeckillConfig(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
