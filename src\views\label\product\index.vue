<template>
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="关键字" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入参数名称" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" @click="openForm" v-hasPermi="['tag:update']"><Icon icon="ep:connection" class="mr-5px" /> 关联商品</el-button>
        <el-button type="primary" :disabled="!multipleSelection.length" v-hasPermi="['tag:update']" @click="handleCancelRelation"><Icon icon="ep:connection" class="mr-5px" />取消关联</el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading" v-hasPermi="['tag:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" @selection-change="selectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="商品主图" width="150" prop="imgUrl">
        <template #default="{ row }">
          <el-image :src="row.imgUrl" class="h-30px w-30px" @click="imagePreview(row.imgUrl)" />
        </template>
      </el-table-column>
      <el-table-column label="商品id" align="center" prop="relationId" width="150" />
      <el-table-column label="状态" align="center" prop="saleStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SALE_STATUS" :value="scope.row.saleStatus" />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="left" prop="title" show-overflow-tooltip />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="paginationData.pageTotal"
      v-model:page="paginationData.pageNum"
      v-model:limit="paginationData.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProductDataForm ref="formRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import download from '@/utils/download'
import * as TagApi from '@/api/tag'
import ProductDataForm from './productDataForm.vue'
import { createImageViewer } from '@/components/ImageViewer'

defineOptions({ name: 'TagProduct' })

const message = useMessage() // 消息弹窗
// const { t } = useI18n() // 国际化
const route = useRoute() // 路由

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  name: '',
  tagId: route.params.id,
  type: 1
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const dictTypeList = ref<TagApi.ProductTagVO[]>() // 字典类型的列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        name: queryParams.name || undefined,
        tagId: queryParams.tagId || undefined,
        type: queryParams.type || undefined
      }
    }
    const data = await TagApi.getProductPage(params)
    list.value = data.pageContents
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

const multipleSelection = ref([])
const selectionChange = (val:[]) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 关联商品*/
const formRef = ref()
const openForm = () => {
  formRef.value.open(queryParams.tagId)
}

const handleCancelRelation = () => {
  const relationIds = multipleSelection.value.map(el => el.relationId)
  TagApi.deleteRelation({
    tagId: queryParams.tagId,
    relationIds,
    type: 1
  }).then(() => {
    handleQuery()
  }).catch(err => {
    console.log(err)
  })
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TagApi.exportProduct(queryParams)
    download.excel(data, '字典数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 查询字典（精简)列表
  dictTypeList.value = await TagApi.getSimpleDictTypeList()
})
// 查看图片
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    zIndex: 9999999,
    urlList: [imgUrl]
  })
}
</script>
