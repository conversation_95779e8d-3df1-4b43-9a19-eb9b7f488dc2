<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :align-center="true" custom-class="relation-dialog" width="650px">
    <ContentWrap :bottom="false" :border="false">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="类目名称" prop="nameKeyword">
          <el-input v-model="queryParams.nameKeyword" placeholder="请输入类目名称" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />重置</el-button>
          <el-button type="primary" @click="handleRelation"><Icon icon="ep:connection" class="mr-5px" />关联</el-button>
        </el-form-item>
      </el-form>
      <div class="tree-box">
        <el-tree
          v-loading="loading"
          ref="treeRef"
          style="max-width: 600px"
          :data="treeData"
          :props="props"
          show-checkbox
          node-key="categoryId"
          :filter-node-method="filterNode"
        />
      </div>

    </ContentWrap>
  </Dialog>
</template>
<script lang="ts" setup>
import * as TagApi from '@/api/tag'
import { ElLoading } from 'element-plus'

defineOptions({ name: 'SystemProductForm' })

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('关联类目') // 弹窗的标题

const queryFormRef = ref() // 表单 Ref
const tagId = ref('')

const queryParams = reactive({
  tagId: '',
  nameKeyword: ''
})

const treeData = ref<{}[]>([])
const treeRef = ref()
const props = {
  label: 'name',
  children: 'children',
}
const fetchSupplyCategory = () => {
  loading.value = true
  TagApi.categoryTree({ tagId: queryParams.tagId, parentId: 0, platform: 'YHT', }).then(res => {
    console.log(56, res)
    treeData.value = res|| []
    nextTick(() => {
      loading.value = false
      if (treeRef.value) {
        treeRef.value.filter()
      }
    })
  }).catch(err => {
    loading.value = false
    console.log(err)
  })
}
const filterNode = (value: string, data: Tree) => {
  let flag = !data.isTagged && data.leaf
  if (value) {
    flag = data.name.includes(value)
  }
  return flag
}

const loading = ref(false)
/** 打开弹窗 */
const open = async (id: string) => {
  dialogVisible.value = true
  tagId.value = id
  queryParams.tagId = id
  queryParams.nameKeyword = ''
  fetchSupplyCategory()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 搜索按钮操作 */
const handleQuery = () => {
  treeRef.value.filter(queryParams.nameKeyword)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
const handleRelation = () => {
  const list = treeRef.value!.getCheckedNodes(false, false) || []
  if (!list.length) return
  const relationIds = list.filter(node => !node.isTagged && node.leaf).map(el => el.categoryId)
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  TagApi.saveRelation({
    tagId: queryParams.tagId,
    relationIds,
    type: 2
  }).then(() => {
    setTimeout(() => {
      loading.close()
      fetchSupplyCategory()
      emit('success')
    }, 1500)
  }).catch(err => {
    loading.close()
    console.log(err)
  })
}
</script>
<style lang="scss">
.divider{
  width: 100%;
  height: 1px;
  margin: 10px 0;
  background: #dcdfe6;
}
.el-overlay {
  .el-overlay-dialog{
    .relation-dialog{
      .el-dialog__body{
        padding: 0 !important;
      }
    }
  }
}
.tree-box{
  min-height: 200px;
  max-height: 600px;
  overflow-y: auto;
}
</style>
