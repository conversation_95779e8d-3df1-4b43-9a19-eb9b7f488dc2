<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="平台" prop="platform">
        <el-select
          v-model="queryParams.platform"
          placeholder="请选择平台"
          clearable
          style="width: 150px"
          @change="handlePlatformChange"
        >
          <el-option
            v-for="item in platformOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入类目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleSearch"
          @clear="handleClearSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="handleReset">
          <Icon class="mr-5px" icon="ep:refresh-right" />
          重置
        </el-button>
        <el-button @click="fetchCategoryList">
          <Icon class="mr-5px" icon="ep:refresh" />
          刷新
        </el-button>
        <el-button v-hasPermi="['system:menu:create']" plain type="primary" @click="createForm(undefined, '0', 0)">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
        <el-button plain type="danger" @click="toggleExpandAll">
          <Icon class="mr-5px" icon="ep:sort" />
          展开/折叠
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 搜索结果提示 -->
  <ContentWrap v-if="searchResults.length > 0">
    <el-alert
      :title="`搜索结果：找到 ${searchResults.length} 个匹配的类目，已自动展开相关节点`"
      type="success"
      :closable="false"
      show-icon
    />
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-auto-resizer>
      <template #default="{width}">
        <el-table-v2
            v-loading="loading"
          v-model:expanded-row-keys="expandedRowKeys"
          :columns="columns"
          :data="treeData"
          :expand-column-key="columns[0].key"
          :height="1000"
          :width="width"
          fixed
          row-key="id"
        />
      </template>
    </el-auto-resizer>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CategoryForm ref="formRef" :tree="treeData" @success="refreshData" />
</template>
<script lang="tsx" setup>
import { DICT_TYPE } from '@/utils/dict'
import * as CategoryApi from '@/api/category'
import CategoryForm from './CategoryForm.vue'
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { Icon } from '@/components/Icon'
import { ElButton, TableV2FixedDir } from 'element-plus'
import { createImageViewer } from '@/components/ImageViewer'
import { checkPermi } from '@/utils/permission'
import {TreeDataHelper} from "@/utils"
import { nextTick } from 'vue'

defineOptions({ name: 'SystemMenu' })

// const { wsCache } = useCache()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const queryParams = reactive({
  name: undefined,
  status: undefined,
  platform: 'YHT' // 默认选择抖音平台
})

// 平台选项
const platformOptions = [
  { label: '源汇通', value: 'YHT' },
  { label: '抖音', value: 'FXG' },
  { label: '快手', value: 'KWAISHOP' },
  { label: '小红书', value: 'XHS' },
  { label: '视频号小店', value: 'SPHXD' }
]
const queryFormRef = ref() // 搜索的表单
const isExpandAll = ref(false) // 是否展开，默认全部折叠
// const refreshTable = ref(true) // 重新渲染表格状态

// 添加展开行控制
const expandedRowKeys = ref<number[]>([])

// 虚拟列表表格
const columns = [
  {
    key: 'name',
    title: '类目名称',
    dataKey: 'name',
    minWidth: 250,
    width: 250,
    fixed: TableV2FixedDir.LEFT,
    cellRenderer: ({ cellData: name, rowData }) => {
      const isMatched = rowData.isSearchMatched
      return (
        <span style={{
          backgroundColor: isMatched ? '#fff2e8' : 'transparent',
          color: isMatched ? '#fa8c16' : 'inherit',
          fontWeight: isMatched ? 'bold' : 'normal',
          padding: isMatched ? '2px 4px' : '0',
          borderRadius: isMatched ? '3px' : '0'
        }}>
          {name}
        </span>
      )
    }
  },
  {
    key: 'id',
    title: '类目id',
    dataKey: 'id',
    width: 200
  },
  {
    key: 'platform',
    title: '平台',
    dataKey: 'platform',
    width: 100,
    align: 'center'
  },
  {
    key: 'imageUrl',
    title: '类目图片',
    dataKey: 'imageUrl',
    width: 120,
    align: 'center',
    cellRenderer: ({ cellData: imageUrl }) => {
      return (
          <el-image src={imageUrl} loading="lazy" class="h-30px w-30px mr-5px" onClick={() => imagePreview(imageUrl)} />
      )
    }
  },
  {
    key: 'level',
    title: '类目等级',
    dataKey: 'level',
    width: 100,
    align: 'center'
  },
  {
    key: 'leaf',
    title: '是否叶子节点',
    dataKey: 'leaf',
    width: 100,
    align: 'center',
    cellRenderer: ({ rowData }) => {
      return <DictTag type={DICT_TYPE.WHETHER_STATUS} value={rowData.leaf} />
    }
  },
  {
    key: 'showStatus',
    title: '是否显示',
    dataKey: 'showStatus',
    width: 100,
    align: 'center',
    cellRenderer: ({ rowData }) => {
      return <DictTag type={DICT_TYPE.VISIBLE_STATUS} value={rowData.showStatus} />
    }
  },
  {
    key: 'operations',
    title: '操作',
    align: 'center',
    width: 160,
    fixed: TableV2FixedDir.RIGHT,
    cellRenderer: ({ rowData }) => {
      // 定义按钮列表
      const buttons: VNode[] = []

      // 检查权限并添加按钮
      if (checkPermi(['category:update'])) {
        buttons.push(
            <ElButton key="edit" link type="primary" onClick={() => openForm('update', rowData.id)}>
              修改
            </ElButton>
        )
      }
      if (checkPermi(['category:create'])) {
        buttons.push(
            <ElButton
                key="create"
                link
                type="primary"
                onClick={() => createForm(undefined, rowData.id, rowData.level + 1)}
            >
              新增
            </ElButton>
        )
      }
      if (checkPermi(['category:delete'])) {
        buttons.push(
            <ElButton key="delete" link type="danger" onClick={() => handleDelete(rowData.id)}>
              删除
            </ElButton>
        )
      }
      // 如果没有权限，返回 null
      if (buttons.length === 0) {
        return null
      }
      // 渲染按钮列表
      return <>{buttons}</>
    }
  }
]

// const tableRef = ref(null)
const treeData = ref<any[]>([])
const originalTreeData = ref<any[]>([]) // 保存原始数据
const searchResults = ref<any[]>([]) // 搜索结果

const fetchCategoryList = async () => {
  loading.value = true
  try {
    const list = await fetchCategoryTree('0')
    treeData.value = [...list]
    originalTreeData.value = [...list] // 保存原始数据
    loading.value = false
  } catch (e) {
    treeData.value = []
    originalTreeData.value = []
    loading.value = false
  }
}
const handleCategoryTree = (arr) => {
  arr.forEach((item) => {
    // item.hasChildren = item.leaf !== 1
    if (item.children?.length > 0) {
      item.children = handleCategoryTree(item.children)
    }
  })
  return arr
}
const fetchCategoryTree = async (parentId) => {
  try {
    const res = await CategoryApi.categoryTree({ platform: queryParams.platform, parentId })
    return handleCategoryTree(res || [])
  } catch (e) {
    return []
  }
}

const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}

/** 平台变更处理 */
const handlePlatformChange = () => {
  // 清空搜索状态
  queryParams.name = ''
  searchResults.value = []
  expandedRowKeys.value = []
  // 平台变更时重新加载类目列表
  fetchCategoryList()
}

/** 搜索功能 */
// 在树形数据中搜索节点并收集路径
const searchInTree = (tree: any[], keyword: string): any[] => {
  const matchedPaths: any[] = []

  const search = (nodes: any[], path: any[] = []): void => {
    nodes.forEach(node => {
      const currentPath = [...path, node]

      // 检查当前节点是否匹配
      if (node.name && node.name.toLowerCase().includes(keyword.toLowerCase())) {
        matchedPaths.push(currentPath)
      }

      // 递归搜索子节点
      if (node.children && node.children.length > 0) {
        search(node.children, currentPath)
      }
    })
  }

  search(tree)
  return matchedPaths
}

// 根据搜索结果构建过滤后的树形结构
const buildFilteredTree = (matchedPaths: any[]): any[] => {
  if (matchedPaths.length === 0) return []

  const allNodes = new Map()
  const expandedIds = new Set<number>()

  // 收集所有需要显示的节点（匹配节点及其所有父级）
  matchedPaths.forEach(path => {
    path.forEach((node: any, index: number) => {
      const nodeId = node.id

      if (!allNodes.has(nodeId)) {
        allNodes.set(nodeId, {
          ...node,
          isSearchMatched: false,
          isInSearchPath: true
        })
      }

      // 标记匹配的节点（路径中的最后一个节点）
      if (index === path.length - 1) {
        allNodes.get(nodeId).isSearchMatched = true
      }

      // 添加到展开列表（除了叶子节点）
      if (index < path.length - 1) {
        expandedIds.add(nodeId)
      }
    })
  })

  // 构建父子关系映射
  const parentChildMap = new Map()
  matchedPaths.forEach(path => {
    for (let i = 0; i < path.length; i++) {
      const currentNode = path[i]
      const parentNode = i > 0 ? path[i - 1] : null
      const parentId = parentNode ? parentNode.id : null

      if (!parentChildMap.has(parentId)) {
        parentChildMap.set(parentId, new Set())
      }
      parentChildMap.get(parentId).add(currentNode.id)
    }
  })

  // 递归构建树形结构
  const buildTree = (parentId: any = null): any[] => {
    const children: any[] = []
    const childIds = parentChildMap.get(parentId) || new Set()

    childIds.forEach(childId => {
      if (allNodes.has(childId)) {
        const node = allNodes.get(childId)
        const childNodes = buildTree(childId)

        children.push({
          ...node,
          children: childNodes
        })
      }
    })

    return children
  }

  const filteredTree = buildTree()

  // 设置展开的节点
  nextTick(() => {
    expandedRowKeys.value = Array.from(expandedIds)
  })

  return filteredTree
}

// 处理搜索
const handleSearch = () => {
  if (!queryParams.name || queryParams.name.trim() === '') {
    handleClearSearch()
    return
  }

  const keyword = queryParams.name.trim()
  const matchedPaths = searchInTree(originalTreeData.value, keyword)

  if (matchedPaths.length > 0) {
    // 构建过滤后的树形结构，只显示匹配项及其父级
    treeData.value = buildFilteredTree(matchedPaths)
    searchResults.value = matchedPaths.map(path => path[path.length - 1]) // 保存匹配的节点
    message.success(`找到 ${matchedPaths.length} 个匹配的类目`)
  } else {
    message.warning('未找到匹配的类目')
    treeData.value = [...originalTreeData.value]
    searchResults.value = []
    expandedRowKeys.value = []
  }
}

// 清空搜索
const handleClearSearch = () => {
  queryParams.name = ''
  searchResults.value = []
  treeData.value = [...originalTreeData.value]
  expandedRowKeys.value = []
}

// 重置搜索
const handleReset = () => {
  queryParams.name = ''
  handleClearSearch()
}

/** 添加/修改操作 */
const formRef = ref()
const createForm = (id: number | undefined, parentId: string, level: 0) => {
  const obj = { id, parentId, level, platform: queryParams.platform }
  console.log(199, 'createForm', obj)
  openForm('create', id, obj)
}
const openForm = (type: string, id: number | undefined, obj?: object) => {
  console.log(199, 'openForm', obj)
  formRef.value.open(type, id, obj)
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  if (!isExpandAll.value) {
    // 展开所有
    expandedRowKeys.value = treeData.value.map((item) => item.id)
  } else {
    // 折叠所有
    expandedRowKeys.value = []
  }
  isExpandAll.value = !isExpandAll.value
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CategoryApi.deleteCategory({ categoryId: id, platform: 'YHT' })
    message.success(t('common.delSuccess'))
    // 更新本地数据
    treeData.value = TreeDataHelper.deleteTreeNode(treeData.value, id)
  } catch (err) {
    console.log(err)
  }
}

// 更新数据
const refreshData = (obj: object, type: string) => {
  treeData.value = TreeDataHelper.upsertTreeNode(treeData.value, obj, { isNew: type === 'create' })
}

/** 初始化 **/
onMounted(() => {
  fetchCategoryList()
})
</script>
