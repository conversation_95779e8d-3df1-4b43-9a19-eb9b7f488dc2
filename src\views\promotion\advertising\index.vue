<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="广告编号" prop="code">
        <el-input
          v-model="queryParams.code"
          class="!w-240px"
          clearable
          placeholder="请输入广告编号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入广告名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="created">
        <el-date-picker
          v-model="queryParams.created"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          v-hasPermi="['promotion:advertising:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true">
      <el-table-column align="center" label="ID" min-width="180" prop="adPlaceId" />
      <el-table-column align="center" label="编号" min-width="180" prop="code" />
      <el-table-column align="center" label="广告名称" min-width="180" prop="name" />
      <el-table-column align="center" label="封面图" width="200" prop="coverImages">
        <template #default="{ row }">
          <el-image v-for="img in row.coverImages" :key="img" :src="img" loading="lazy" class="h-30px w-30px mr-5px" @click="imagePreview(img)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="广告位置" min-width="180" prop="positionName" />
      <el-table-column align="center" label="广告简介" min-width="250" prop="intro" />
      <el-table-column align="center" label="详情" min-width="250" prop="detail" />
      <el-table-column align="center" label="排序" min-width="60" prop="sort" />
      <el-table-column align="center" label="状态" min-width="100" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PUBLISHED_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="发布时间"
        prop="publishTime"
        width="180px"
      />
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button
            v-hasPermi="['promotion:advertising:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.adPlaceId)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['promotion:advertising:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.adPlaceId)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AdvertisingForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as AdvertisingApi from '@/api/promotion/advertising'
import AdvertisingForm from './AdvertisingForm.vue'
import { createImageViewer } from '@/components/ImageViewer'

defineOptions({ name: 'PromotionAdvertising' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  code: undefined,
  name: undefined,
  publishStartTime: undefined,
  publishEndTime: undefined,
  created: []
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
/** 广告封面预览 */
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        code: queryParams.code,
        name: queryParams.name,
        publishStartTime: queryParams.created[0],
        publishEndTime: queryParams.created[1],
      }
    }
    const data = await AdvertisingApi.getAdvertisingPage(params)
    // list.value = data.pageContents || []
    const arr = data.pageContents || []
    list.value = arr.map(item => {
      console.log(178, item.coverImages)
      item.coverImages = JSON.parse(item.coverImages || '[]')
      return item
    })
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AdvertisingApi.deleteAdvertising(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const categoryList = ref<AdvertisingApi.AdvertisingCategoryVO[]>([])
onMounted(async () => {
  await getList()
  // 加载分类、商品列表
  categoryList.value =
    (await AdvertisingApi.getSimpleAdvertisingCategoryList()) as AdvertisingApi.AdvertisingCategoryVO[]
})
</script>
