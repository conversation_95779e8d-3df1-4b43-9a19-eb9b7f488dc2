<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="500">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="150px"
    >
      <el-form-item label="费率配置名称" prop="feeName">
        <el-input v-model="formData.feeName" class="!w-240px" placeholder="请输入费率配置名称" />
      </el-form-item>
      <el-form-item label="收入费率百分比" prop="incomeFeeRate">
        <el-input-number v-model="formData.incomeFeeRate" class="!w-240px" :min="0" :max="100" placeholder="请输入收入费率百分比" />
      </el-form-item>
      <el-form-item label="收入固定费用金额(分)" prop="incomeFeeFixed">
        <el-input-number v-model="formData.incomeFeeFixed" class="!w-240px" :min="0" placeholder="请输入收入固定费用金额" />
      </el-form-item>
      <el-form-item label="支出费率百分比" prop="expenseFeeRate">
        <el-input-number v-model="formData.expenseFeeRate" class="!w-240px" :min="0" :max="100" placeholder="请输入支出费率百分比" />
      </el-form-item>
      <el-form-item label="支出固定费用金额(分)" prop="expenseFeeFixed">
        <el-input-number v-model="formData.expenseFeeFixed" class="!w-240px" :min="0" placeholder="请输入支出固定费用金额" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as SupplierApi from '@/api/supplier'

defineOptions({ name: 'FeeRateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  feeName: undefined,
  incomeFeeRate: undefined,
  incomeFeeFixed: undefined,
  expenseFeeRate: undefined,
  expenseFeeFixed: undefined,
})
const formRules = reactive({
  typeCode: [{ required: true, message: '内容类型不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '内容标题不能为空', trigger: 'blur' }]
  // sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  // status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  // content: [{ required: true, message: '内容内容不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: object) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SupplierApi.getFeeConfig(id)
      console.log(101, 'formData', formData.value)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await SupplierApi.createFeeConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await SupplierApi.updateFeeConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    feeName: undefined,
    incomeFeeRate: undefined,
    incomeFeeFixed: undefined,
    expenseFeeRate: undefined,
    expenseFeeFixed: undefined,
  }
  formRef.value?.resetFields()
}
// const spuList = ref<ProductSpuApi.Spu[]>([])
onMounted(async () => {})
</script>
