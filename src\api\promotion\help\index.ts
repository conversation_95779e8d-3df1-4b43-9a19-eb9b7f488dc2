import request from '@/config/axios'

export interface HelpVO {
  id: number
  categoryId: number
  title: string
  author: string
  picUrl: string
  introduction: string
  browseCount: string
  sort: number
  status: number
  spuId: number
  recommendHot: boolean
  recommendBanner: boolean
  content: string
}

export interface HelpCategoryVO {
  id: number
  typeName: string
  code: string
  picUrl: string
  isVisible: number
  sortOrder: number
  typeImage: string
  description: string
}

// 查询帮助列表
export const getHelpPage = async (data) => {
  return await request.post({ url: `/v1/platform/infra/help/page`, data })
}

// 查询帮助详情
export const getHelp = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/help/get?id=` + id })
}

// 新增帮助
export const createHelp = async (data: HelpVO) => {
  return await request.post({ url: `/v1/platform/infra/help/create`, data })
}

// 修改帮助
export const updateHelp = async (data: HelpVO) => {
  return await request.post({ url: `/v1/platform/infra/help/update`, data })
}

// 删除帮助
export const deleteHelp = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/help/delete?id=` + id })
}


// 查询帮助类型列表
export const getHelpCategoryPage = async (data) => {
  return await request.post({ url: `/v1/platform/infra/help/type/page`, data })
}

// 查询帮助类型精简信息列表
export const getSimpleHelpCategoryList = async () => {
  return await request.get({ url: `/v1/platform/infra/help/type/list-visible` })
}

// 查询帮助类型详情
export const getHelpCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/help/type/get?id=` + id })
}

// 新增帮助类型
export const createHelpCategory = async (data: HelpCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/help/type/create`, data })
}

// 修改帮助类型
export const updateHelpCategory = async (data: HelpCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/help/type/update`, data })
}

// 删除帮助类型
export const deleteHelpCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/help/type/delete?id=` + id })
}
