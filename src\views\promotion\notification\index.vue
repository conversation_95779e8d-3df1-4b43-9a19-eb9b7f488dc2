<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="消息标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入消息标题" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item label="推送时间" prop="created">
        <el-date-picker
          v-model="queryParams.created"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['promotion:notification:create']">
          <Icon icon="ep:plus" class="mr-5px" />新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <!--<el-table-column label="模板编码" align="center" prop="code" width="120" :show-overflow-tooltip="true" />-->
      <el-table-column label="消息标题" align="center" prop="title" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="消息类型" align="center" prop="messageType" width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MESSAGE_TYPE" :value="scope.row.messageType" />
        </template>
      </el-table-column>
      <el-table-column label="消息内容" prop="content" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column label="推送次数" prop="pushCount" width="100" />
      <el-table-column label="推送量" prop="pushVolume" width="100" />
      <el-table-column label="点击量" prop="clickCount" width="100" />
      <el-table-column label="推送状态" align="center" prop="pushStatus" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PUSH_STATUS" :value="scope.row.pushStatus" />
        </template>
      </el-table-column>
      <el-table-column label="开启状态" align="center" prop="isEnabled" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.isEnabled" />
        </template>
      </el-table-column>
      <el-table-column label="推送时间" align="center" prop="pushTime" width="180" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center" width="210" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="sendMessage(scope.row.id)" v-hasPermi="['promotion:notification:update']">发送</el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)" v-hasPermi="['promotion:notification:update']">修改</el-button>
          <!--<el-button link type="primary" @click="openSendForm(scope.row)" v-hasPermi="['promotion:notification:send-notify']">测试</el-button>-->
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['promotion:notification:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="paginationData.pageTotal"
      v-model:page="paginationData.pageNum"
      v-model:limit="paginationData.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <NotificationForm ref="formRef" @success="getList" />
  <!-- 表单弹窗：测试发送 -->
  <NotificationSendForm ref="sendFormRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as NotifyTemplateApi from '@/api/system/notify/template'
import NotificationForm from './NotificationForm.vue'
import NotificationSendForm from './NotificationSendForm.vue'
// import { sendNotify } from '@/api/system/notify/template'

defineOptions({ name: 'Notification' })

const message = useMessage() // 消息弹窗

const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  title: undefined,
  created: []
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        title: queryParams.title || undefined,
        pushStartTime: queryParams.created[0],
        pushEndTime: queryParams.created[1],
        // created: queryParams.created?.length ? queryParams.created : undefined
      }
    }
    const data = await NotifyTemplateApi.getNotifyTemplatePage(params)
    list.value = data.pageContents
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const sendMessage = (id: number) => {
  NotifyTemplateApi.sendNotify(id).then(() => {
    message.success('发送成功')
    getList()
  })
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  console.log(152, type, id)
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await NotifyTemplateApi.deleteNotifyTemplate(id)
    message.success('删除成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 发送站内信按钮 */
const sendFormRef = ref() // 表单 Ref
const openSendForm = (row: NotifyTemplateApi.NotifyTemplateVO) => {
  sendFormRef.value.open(row.id)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
