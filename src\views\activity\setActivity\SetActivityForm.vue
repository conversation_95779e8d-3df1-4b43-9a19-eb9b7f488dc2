<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="活动名称" prop="title">
        <el-input v-model="formData.title" placeholder="请输入活动名称" />
      </el-form-item>
      <el-form-item label="活动编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入活动编码" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker v-model="formData.startTime" type="date" value-format="YYYY-MM-DD 00:00:00" placeholder="选择开始时间" />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker v-model="formData.endTime" type="date" value-format="YYYY-MM-DD 23:59:59" placeholder="选择结束时间" />
      </el-form-item>
      <el-form-item label="推荐位置" prop="recommendPositions">
        <el-checkbox-group v-model="formData.recommendPositions">
          <el-checkbox
            v-for="dict in getStrDictOptions(DICT_TYPE.RECOMMENDED_LOCATION)"
            :key="dict.value" :value="dict.value" :label="dict.label"
          />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="上架状态" prop="shelfStatus">
        <el-radio-group v-model="formData.shelfStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SALE_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入活动备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import * as SeckillActivityApi from '@/api/activity/seckillActivity'

/** 秒杀时段 表单 */
defineOptions({ name: 'SeckillConfigForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  title: undefined,
  startTime: undefined,
  endTime: undefined,
  recommendPositions: [],
  remark: undefined,
  shelfStatus: undefined
})
const formRules = reactive({
  code: [{ required: true, message: '活动编号不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '活动名称不能为空', trigger: 'blur' }],
  startTime: [{ required: true, message: '开始时间点不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '结束时间点不能为空', trigger: 'blur' }],
  shelfStatus: [{ required: true, message: '上架状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const obj = await SeckillActivityApi.getSeckillConfig(id)
      obj.recommendPositions = JSON.parse(obj.recommendPositions || '[]')
      formData.value = obj
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SeckillActivityApi.SeckillConfigVO
    data.recommendPositions = data.recommendPositions ? JSON.stringify(data.recommendPositions) : undefined
    if (formType.value === 'create') {
      await SeckillActivityApi.createSeckillConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await SeckillActivityApi.updateSeckillConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    title: undefined,
    startTime: undefined,
    endTime: undefined,
    recommendPositions: [],
    remark: undefined,
    shelfStatus: undefined
  }
  formRef.value?.resetFields()
}
</script>
