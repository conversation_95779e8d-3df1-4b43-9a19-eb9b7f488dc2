# 修改日志

## 2025-08-01 - 添加批量更新类目显示状态功能

### 修改内容

#### 1. API接口添加 (`src/api/category/index.ts`)
- 新增 `updateShowStatusRecursion` 函数
- 调用接口：`/v1/platform/product/category/update/show-status/recursion`
- 请求参数：
  - `parentId`: 父级类目ID (string)
  - `platform`: 平台 (string) 
  - `showStatus`: 显示状态，0-隐藏，1-显示 (number)

#### 2. 类目管理页面功能增强 (`src/views/categories/index.vue`)
- 在操作列添加"批量更新显示状态"按钮
- 新增 `handleUpdateShowStatus` 处理函数
- 功能特性：
  - 支持递归更新选中类目及其所有子类目的显示状态
  - 用户友好的输入提示框，支持输入验证
  - 操作确认机制，防止误操作
  - 操作完成后自动刷新数据
- 调整操作列宽度从160px增加到280px以容纳新按钮
- 导入 `ElMessageBox` 组件用于用户交互

### 技术实现细节

1. **权限控制**: 使用 `checkPermi(['category:update'])` 确保只有有权限的用户才能看到按钮
2. **用户体验**: 
   - 使用 `ElMessageBox.prompt` 提供直观的输入界面
   - 输入验证确保只能输入0或1
   - 默认值为当前类目的显示状态
3. **错误处理**: 完善的try-catch错误处理和用户提示
4. **数据刷新**: 操作成功后自动调用 `fetchCategoryList()` 刷新页面数据

### API文档信息
- **接口路径**: `/v1/platform/product/category/update/show-status/recursion`
- **请求方法**: POST
- **功能描述**: 更新显示状态（递归更新子类目）
- **请求头**: 需要 Authorization Bearer Token
- **响应格式**: 标准响应格式，成功时返回code: 0

### 使用说明
1. 在类目管理页面找到需要批量更新的类目
2. 点击该类目行的"批量更新显示状态"按钮
3. 在弹出的对话框中输入显示状态（0-隐藏，1-显示）
4. 点击确定执行批量更新
5. 系统会递归更新该类目及其所有子类目的显示状态
