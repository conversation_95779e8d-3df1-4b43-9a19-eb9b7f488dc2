<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item>
        <el-button @click="handleRefresh"><Icon icon="ep:refresh" class="mr-5px" />刷新</el-button>
        <el-button type="primary" plain @click="openForm" v-hasPermi="['activity:setProduct:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 添加商品
        </el-button>
        <el-button type="danger" :disabled="!multipleSelection.length" v-hasPermi="['activity:setProduct:delete']" @click="handleBatchDelete"><Icon icon="ep:delete" class="mr-5px" />批量删除商品</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" row-key="id" :stripe="true"  @selection-change="selectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="商品ID" prop="activityProductId" width="150" />
      <el-table-column align="center" label="商品图片" width="80" prop="imgUrl" class-name="!p-0" show-overflow-tooltip>
        <template #default="{ row }">
          <el-image :src="row.imgUrl" loading="lazy" class="h-30px align-bottom" fit="contain" @click="imagePreview(row.imgUrl)" />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" prop="productName" min-width="140" show-overflow-tooltip />
      <el-table-column label="商品价格" prop="normalPrice" width="100" :formatter="fenToYuanFormat" />
      <el-table-column label="秒杀价" prop="activityPrice"  width="150">
        <template #default="scope">
          <el-input-number v-if="scope.row.edit" v-model="scope.row.activityPrice" :min="0" :step="1" step-strictly size="small" />
          <template v-else>
            {{ `￥${scope.row.activityPrice.toFixed(2)}` }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="秒杀数量" prop="activityStock"  width="150">
        <template #default="scope">
          <el-input-number v-if="scope.row.edit" v-model="scope.row.activityStock" :min="0" :step="1" step-strictly size="small" />
          <template v-else>
            {{ scope.row.activityStock }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="活动剩余库存" prop="remainingStock"  width="150" />
      <el-table-column label="商品库存" prop="productStock" width="100" />
      <el-table-column label="活动库存预警值" prop="stockWarningValue"  width="150">
        <template #default="scope">
          <el-input-number v-if="scope.row.edit" v-model="scope.row.stockWarningValue" :min="0" :step="1" step-strictly size="small" />
          <template v-else>
            {{ scope.row.stockWarningValue }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="限购数量" prop="purchaseLimit"  width="150">
        <template #default="scope">
          <el-input-number v-if="scope.row.edit" v-model="scope.row.purchaseLimit" :min="0" :step="1" step-strictly size="small" />
          <template v-else>
            {{ scope.row.purchaseLimit }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort"  width="150">
        <template #default="scope">
          <el-input-number v-if="scope.row.edit" v-model="scope.row.sort" :min="0" :step="1" step-strictly size="small" />
          <template v-else>
            {{ scope.row.sort }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150px" fixed="right">
        <template #default="scope">
          <template v-if="scope.row.edit">
            <el-button link type="primary" v-hasPermi="['activity:setProduct:update']" @click="handleSave(scope.row)">保存</el-button>
            <el-button link type="primary" v-hasPermi="['activity:setProduct:update']" @click="handleEdit(scope.row, false)">取消</el-button>
          </template>
          <template v-else>
            <el-button link type="primary" v-hasPermi="['activity:setProduct:update']" @click="handleEdit(scope.row, true)">编辑</el-button>
            <el-button link type="danger" v-hasPermi="['activity:setProduct:delete']" @click="handleDelete(scope.row.activityProductId)">删除</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="paginationData.pageTotal"
      v-model:page="paginationData.pageNum"
      v-model:limit="paginationData.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 表单弹窗：添加/修改 -->
  <ProductDataForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import * as SeckillActivityApi from '@/api/activity/seckillActivity'
import ProductDataForm from './productDataForm.vue'
import { fenToYuanFormat } from '@/utils/formatter'
import { ElLoading } from 'element-plus'
import { createImageViewer } from '@/components/ImageViewer'

defineOptions({ name: 'SetProduct' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute() // 路由

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  activityId: route.params.activityId,
  activityTimeSlotId: route.params.id,
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
// const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: queryParams
    }
    const data = await SeckillActivityApi.getActivityProductPage(params)
    list.value = (data.pageContents || []).map(item => {
      return {
        ...item,
        edit: false,
        activityPrice: item.activityPrice / 100
      }
    })
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 广告封面预览 */
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}

/** 刷新按钮操作 */
const handleRefresh = () => {
  paginationData.pageNum = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = () => {
  formRef.value.open()
}

const multipleSelection = ref([])
const selectionChange = (val:[]) => {
  multipleSelection.value = val
}
const handleBatchDelete = async () => {
  // 删除的二次确认
  await message.delConfirm()
  try {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const ids = multipleSelection.value.map(el => el.activityProductId)
    // 发起删除
    await SeckillActivityApi.deleteActivityProduct({ ids })
    loading.close()
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch (e) {
    loading.close()
  }
}
const selectRowMap = ref({})
const handleEdit = (row, bol) => {
  if (bol) {
    selectRowMap.value[row.id] = { ...row }
  } else {
    row.activityPrice = selectRowMap.value[row.id].activityPrice
    row.activityStock = selectRowMap.value[row.id].activityStock
    row.stockWarningValue = selectRowMap.value[row.id].stockWarningValue
    row.purchaseLimit = selectRowMap.value[row.id].purchaseLimit
    row.sort = selectRowMap.value[row.id].sort
    selectRowMap.value[row.id] = undefined
  }
  row.edit = bol
}
const handleSave = async (row) => {
  row.edit = false
  try {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const obj = {
      activityProductId: row.activityProductId,
      activityPrice: row.activityPrice * 100,
      activityStock: row.activityStock,
      remainingStock: row.remainingStock,
      stockWarningValue: row.stockWarningValue,
      purchaseLimit: row.purchaseLimit,
      sort: row.sort,
    }
    await SeckillActivityApi.updateActivityProduct(obj)
    loading.close()
    getList()
  } catch (e) {
    loading.close()
  }

}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SeckillActivityApi.deleteActivityProduct({ ids: [id] })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-table) {
  .product-img{
    padding: 0;
    .cell{
      //background: aqua;
    }
  }
}

</style>
