<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="活动标题或编号" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入活动标题或编号" clearable @keyup.enter="handleQuery" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-select v-model="queryParams.enabled" placeholder="请选择启用状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['activity:setActivity:create']"><Icon icon="ep:plus" class="mr-5px" /> 新增</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true">
      <el-table-column label="时间段编号" align="center" prop="code" />
      <el-table-column label="活动名称" align="center" prop="name" min-width="150" show-overflow-tooltip />
      <el-table-column label="每日开始时间" align="center" prop="dailyStartTime" />
      <el-table-column label="每日结束时间" align="center" prop="dailyEndTime" />
      <el-table-column label="活动状态" align="center" prop="enabled" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.enabled" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.activityTimeSlotId)" v-hasPermi="['activity:setActivity:update']">编辑</el-button>
          <router-link :to="`/activity/set-product/${scope.row.activityId}/${scope.row.activityTimeSlotId}`">
            <el-button link type="success" v-hasPermi="['activity:setActivity:update']">商品</el-button>
          </router-link>
          <el-button link type="danger" @click="handleDelete(scope.row.activityTimeSlotId)" v-hasPermi="['activity:setActivity:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="paginationData.pageTotal"
      v-model:page="paginationData.pageNum"
      v-model:limit="paginationData.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TimeRangeForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import * as SeckillActivityApi from '@/api/activity/seckillActivity'
import TimeRangeForm from './TimeRangeForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 秒杀时段 列表 */
defineOptions({ name: 'TimeRange' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<SeckillActivityApi.SeckillConfigVO[]>([]) // 列表的数据
const queryParams = reactive({
  activityId: route.params.id,
  name: undefined,
  enabled: undefined
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        activityId: queryParams.activityId,
        name: queryParams.name || undefined,
        enabled: queryParams.enabled
      }
    }
    const data = await SeckillActivityApi.timeslotPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SeckillActivityApi.deleteTimeslot(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
