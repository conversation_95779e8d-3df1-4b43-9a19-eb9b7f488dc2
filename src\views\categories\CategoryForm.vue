<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item v-if="![0, '0'].includes(formData.parentId)" label="上级类目">
        <el-tree-select
          v-model="formData.parentId"
          :data="props.tree"
          :default-expanded-keys="[0]"
          :props="defaultProps"
          :disabled="formData.parentId"
          check-strictly
          node-key="id"
        />
      </el-form-item>
      <el-form-item label="类目名称" prop="name">
        <el-input v-model="formData.name" clearable placeholder="请输入类目名称" />
      </el-form-item>
      <el-form-item label="类目等级" prop="level">
        <el-input-number v-model="formData.level" disabled :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item label="类目图片" prop="imageUrl">
        <UploadImg v-model="formData.imageUrl" height="80px" />
      </el-form-item>
      <!--<el-form-item label="是否叶子节点" prop="leaf">-->
      <!--  <el-radio-group v-model="formData.leaf">-->
      <!--    <el-radio v-for="dict in getIntDictOptions(DICT_TYPE.WHETHER_STATUS)" :key="dict.label" :value="dict.value">{{ dict.label }}</el-radio>-->
      <!--  </el-radio-group>-->
      <!--</el-form-item>-->
      <el-form-item label="所属平台" prop="platform">
        <el-input v-model="formData.platform" disabled clearable placeholder="请输入所属平台" />
      </el-form-item>
      <el-form-item label="是否显示" prop="showStatus">
        <el-radio-group v-model="formData.showStatus">
          <el-radio v-for="dict in getIntDictOptions(DICT_TYPE.VISIBLE_STATUS)" :key="dict.label" :value="dict.value">{{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as CategoryApi from '@/api/category'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { defaultProps } from '@/utils/tree'

defineOptions({ name: 'SystemMenuForm' })

const props = defineProps({
  tree: {
    type: Array,
    default: () => []
  }
})

const { wsCache } = useCache()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  platform: undefined,
  name: undefined,
  imageUrl: undefined,
  parentId: undefined,
  level: undefined,
  leaf: undefined,
  showStatus: 0
})
const formRules = reactive({
  name: [{ required: true, message: '类目名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id: number | undefined, obj: object) => {
  console.log(80, obj)
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CategoryApi.categoryDetail({ categoryId: id, platform: 'YHT' })
    } finally {
      formLoading.value = false
    }
    console.log(188, formData.value)
  } else {
    formData.value = {
      ...formData.value,
      ...obj
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CategoryApi.ConfigVO
    data.platform = 'YHT'
    let response: {}
    if (formType.value === 'create') {
      response =  await CategoryApi.createCategory(data)
      message.success(t('common.createSuccess'))
    } else {
      response = await CategoryApi.updateCategory(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', response, formType.value)
  } finally {
    formLoading.value = false
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    platform: undefined,
    name: undefined,
    imageUrl: undefined,
    parentId: undefined,
    level: undefined,
    leaf: undefined,
    showStatus: 0
  }
  formRef.value?.resetFields()
}
</script>
