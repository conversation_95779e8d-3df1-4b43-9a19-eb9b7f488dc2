<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="60%">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <el-form-item label="内容标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入内容标题" />
      </el-form-item>
      <el-form-item label="内容类型" prop="typeCode">
        <el-select v-model="formData.typeCode" placeholder="请选择">
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.typeName"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number v-model="formData.sortOrder" :min="0" clearable controls-position="right" />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.PUBLISHED_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="简介" prop="description">
        <el-input v-model="formData.summary" placeholder="请输入类型介绍" type="textarea" />
      </el-form-item>
      <el-form-item label="内容">
        <Editor v-model="formData.content" height="150px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as ArticleApi from '@/api/article'

defineOptions({ name: 'PromotionArticleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  typeCode: undefined,
  title: undefined,
  summary: undefined,
  sortOrder: 0,
  status: 0,
  content: ''
})
const formRules = reactive({
  typeCode: [{ required: true, message: '内容类型不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '内容标题不能为空', trigger: 'blur' }]
  // sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  // status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  // content: [{ required: true, message: '内容内容不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, row?: object) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  const id = row?.id || undefined
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ArticleApi.getArticleDetail(id)
      console.log(101, 'formData', formData.value)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ArticleApi.ArticleVO
    if (formType.value === 'create') {
      await ArticleApi.createArticle(data)
      message.success(t('common.createSuccess'))
    } else {
      await ArticleApi.updateArticle(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    typeCode: undefined,
    title: undefined,
    summary: undefined,
    sortOrder: 0,
    status: 0,
    content: ''
  }
  formRef.value?.resetFields()
}

const categoryList = ref<ArticleApi.ArticleCategoryVO[]>([])
// const spuList = ref<ProductSpuApi.Spu[]>([])
onMounted(async () => {
  categoryList.value = (await ArticleApi.getSimpleArticleCategoryList()) as ArticleApi.ArticleCategoryVO[]
  console.log(215, 'categoryList.value', categoryList.value)
  // spuList.value = (await ProductSpuApi.getSpuSimpleList()) as ProductSpuApi.Spu[]
})
</script>
