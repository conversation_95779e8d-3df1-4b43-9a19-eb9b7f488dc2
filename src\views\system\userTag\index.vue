<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="标签名称" prop="tagName">
        <el-input
          v-model="queryParams.tagName"
          placeholder="请输入标签名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="created">
        <el-date-picker
          v-model="queryParams.created"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:tag:create']">
          <Icon icon="ep:plus" class="mr-5px" />新增
        </el-button>
        <!--<el-button link type="primary" @click="openForm('view')" v-hasPermi="['system:tag:view']">查看</el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="标签ID" align="center" prop="accountTagId" />
      <el-table-column label="标签名称" align="center" prop="tagName" />
      <el-table-column label="会员人数" align="center" prop="memberCount" />
      <!--<el-table-column label="自动打标签人数" align="center" prop="remark" width="120" :show-overflow-tooltip="true" />-->
      <!--<el-table-column label="自动打标签条件" align="center" prop="remark" width="120" :show-overflow-tooltip="true" />-->
      <el-table-column label="操作" align="center" width="250" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('view', scope.row.accountTagId)" v-hasPermi="['system:tag:update']">查看</el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.accountTagId)" v-hasPermi="['system:tag:update']">编辑</el-button>
          <el-button link type="primary" @click="openDataFormRef(scope.row.accountTagId)" v-hasPermi="['system:tag:view']">用户</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.accountTagId)" v-hasPermi="['system:tag:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="paginationData.pageTotal"
      v-model:page="paginationData.pageNum"
      v-model:limit="paginationData.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <UserTagForm ref="formRef" @success="getList" />
  <UserTagDataForm ref="dataFormRef" />
</template>
<script lang="ts" setup>
import * as UserTagApi from '@/api/system/userTag'
import UserTagForm from './UserTagForm.vue'
import UserTagDataForm from './UserTagDataForm.vue'

defineOptions({ name: 'SystemUserTag' })

const message = useMessage() // 消息弹窗

const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  tagName: undefined,
  startTime: undefined,
  endTime: undefined,
  created: []
})
const queryFormRef = ref() // 搜索的表单
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        tagName: queryParams.tagName || undefined,
        startTime: queryParams.created[0] || undefined,
        endTime: queryParams.created[1] || undefined
      }
    }
    const data = await UserTagApi.getTagPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
// 查看关联用户
const dataFormRef = ref()
const openDataFormRef = (id: number) => {
  dataFormRef.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserTagApi.deleteTag(id)
    message.success('删除成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
