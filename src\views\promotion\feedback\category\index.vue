<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px">
      <el-form-item label="类型code" prop="code">
        <el-input
          v-model="queryParams.code"
          class="!w-240px"
          clearable
          placeholder="请输入类型code"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入类型名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="displayFlag">
        <el-select v-model="queryParams.displayFlag" class="!w-240px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.VISIBLE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['promotion:feedback-category:create']" plain type="primary" @click="openForm('create')">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :show-overflow-tooltip="true" :stripe="true">
      <el-table-column align="center" label="编号" prop="id" width="160" />
      <el-table-column label="code" prop="code" width="200" />
      <el-table-column label="类型名称" prop="name" min-width="240" />
      <el-table-column label="类型图标" width="80" prop="icon">
        <template #default="{ row }">
          {{ row.picUrl }}
          <el-image :src="row.icon" loading="lazy" class="h-30px w-30px" @click="imagePreview(row.icon)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="displayFlag" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISIBLE_STATUS" :value="scope.row.displayFlag" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="排序" prop="sort" width="80" />
      <el-table-column :formatter="dateFormatter" align="center" label="更新时间" prop="updated" width="180px" />
      <el-table-column :formatter="dateFormatter" align="center" label="创建时间" prop="created" width="180px" />
      <el-table-column align="center" label="操作" width="120">
        <template #default="scope">
          <el-button v-hasPermi="['promotion:feedback-category:update']" link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button v-hasPermi="['promotion:feedback-category:delete']" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <FeedbackCategoryForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as FeedbackApi from '@/api/promotion/feedback'
import FeedbackCategoryForm from './FeedbackCategoryForm.vue'
import { createImageViewer } from '@/components/ImageViewer'

defineOptions({ name: 'PromotionFeedbackCategory' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  code: null,
  name: null,
  displayFlag: null
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
// const exportLoading = ref(false) // 导出的加载中

/** 类型图预览 */
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        code: queryParams.code || undefined,
        name: queryParams.name || undefined,
        displayFlag: queryParams.displayFlag
      }
    }
    const data = await FeedbackApi.getFeedbackCategoryPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await FeedbackApi.deleteFeedbackCategory(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
