<template>
<!--  <ContentWrap title="类目属性映射">-->
    <el-card>
      <el-form :model="form" inline>
        <el-form-item label="平台" class="!mb-0">
          <el-select
            style="width: 200px"
            v-model="form.platform"
            placeholder="请选择目标平台"
          >
            <el-option
              v-for="item in platformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="!mb-0">
          <el-button type="primary" @click="startTask" :loading="loading">开始映射</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-if="currentTaskId" class="mt-4">
      <template #header>
        <div class="card-header">
          <span>任务进度</span>
          <el-button type="primary" link @click="refreshProgress">刷新进度</el-button>
        </div>
      </template>

      <el-descriptions border :column="2">
        <el-descriptions-item label="任务ID">{{ taskProgress.taskId }}</el-descriptions-item>
        <el-descriptions-item label="平台">{{ taskProgress.platform }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="taskProgress.status === 'INITIALIZED'" type="info">{{ getStatusText(taskProgress.status) }}</el-tag>
          <el-tag v-else-if="taskProgress.status === 'PROCESSING'" type="warning">{{ getStatusText(taskProgress.status) }}</el-tag>
          <el-tag v-else-if="taskProgress.status === 'COMPLETED'" type="success">{{ getStatusText(taskProgress.status) }}</el-tag>
          <el-tag v-else-if="taskProgress.status === 'FAILED'" type="danger">{{ getStatusText(taskProgress.status) }}</el-tag>
          <el-tag v-else>{{ getStatusText(taskProgress.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ formatDate(taskProgress.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="当前进度" :span="2">
          <el-progress 
            :percentage="getProgressPercentage()" 
            :status="getProgressStatus()">
            {{ taskProgress.processedItems }}/{{ taskProgress.totalItems }}
          </el-progress>
        </el-descriptions-item>
        <el-descriptions-item label="当前页/总页数">
          {{ taskProgress.currentPage }}/{{ taskProgress.totalPages }}
        </el-descriptions-item>
        <el-descriptions-item label="创建的映射数">{{ taskProgress.createdMappings }}</el-descriptions-item>
        <el-descriptions-item label="更新的属性数">{{ taskProgress.updatedAttributes }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ formatDate(taskProgress.endTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item v-if="taskProgress.errorMessage" label="错误信息" :span="2">
          <el-alert type="error" :title="taskProgress.errorMessage" :closable="false" />
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
<!--  </ContentWrap>-->
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, onMounted, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { initCategoryAttributeMapping, getTaskProgress, type TaskProgress } from '@/api/product/categoryMapping'

const platformOptions = [
  // { label: '抖音', value: 'FXG' },
  { label: '快手', value: 'KWAISHOP' },
  { label: '小红书', value: 'XHS' },
  { label: '视频号', value: 'SPHXD' }
]

// 接收平台参数
const props = defineProps({
  platform: {
    type: String,
    default: 'KWAISHOP'
  }
})

// 定义事件
const emit = defineEmits(['mapping-completed'])

const form = ref({
  platform: props.platform
})

// 监听props.platform变化
watch(() => props.platform, (newVal) => {
  form.value.platform = newVal
})

const loading = ref(false)
const currentTaskId = ref('')
const taskProgress = ref<TaskProgress>({
  taskId: '',
  platform: '',
  status: '',
  startTime: new Date(),
  endTime: new Date(),
  totalItems: 0,
  processedItems: 0,
  currentPage: 0,
  totalPages: 0,
  createdMappings: 0,
  updatedAttributes: 0,
  errorMessage: ''
})

// 自动刷新定时器
let refreshTimer: number | null = null

// 开始任务
const startTask = async () => {
  if (!form.value.platform) {
    ElMessage.warning('请选择平台')
    return
  }
  
  try {
    loading.value = true
    const res = await initCategoryAttributeMapping(form.value.platform)
    currentTaskId.value = res
    ElMessage.success('任务已启动')
    
    // 立即获取一次进度
    await refreshProgress()
    
    // 设置定时刷新
    startAutoRefresh()
  } catch (error: any) {
    ElMessage.error(error.message || '启动任务失败')
  } finally {
    loading.value = false
  }
}

// 刷新进度
const refreshProgress = async () => {
  if (!currentTaskId.value) return
  
  try {
    const res = await getTaskProgress(currentTaskId.value)
    taskProgress.value = res
    
    // 如果任务已完成或失败，停止自动刷新
    if (res.status === 'COMPLETED' || res.status === 'FAILED') {
      stopAutoRefresh()
      
      // 任务完成时触发事件
      if (res.status === 'COMPLETED') {
        emit('mapping-completed')
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取任务进度失败')
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  refreshTimer = window.setInterval(refreshProgress, 3000)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 计算进度百分比
const getProgressPercentage = () => {
  if (!taskProgress.value.totalItems) return 0
  return Math.floor((taskProgress.value.processedItems / taskProgress.value.totalItems) * 100)
}

// 获取进度条状态
const getProgressStatus = () => {
  if (taskProgress.value.status === 'FAILED') return 'exception'
  if (taskProgress.value.status === 'COMPLETED') return 'success'
  return ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'INITIALIZED': '初始化',
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'FAILED': '失败'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date?: Date) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}:${String(d.getSeconds()).padStart(2, '0')}`
}

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  stopAutoRefresh()
})

// 组件挂载时检查任务状态
onMounted(async () => {
  // 尝试获取当前平台的任务进度
  try {
    const res = await getTaskProgress(props.platform)
    if (res) {
      currentTaskId.value = res.taskId
      taskProgress.value = res
      
      // 如果任务正在进行中，启动自动刷新
      if (res.status === 'PROCESSING' || res.status === 'INITIALIZED') {
        startAutoRefresh()
      }
    }
  } catch (error) {
    // 可能没有正在进行的任务，不做处理
  }
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-4 {
  margin-top: 16px;
}
</style> 
