<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px">
      <el-form-item label="费率配置名称" prop="feeName">
        <el-input v-model="queryParams.feeName" class="!w-240px" clearable placeholder="请输入内容标题" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['supplier:fee-config:create']" plain type="primary" @click="openForm('create')">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true">
      <el-table-column align="center" label="ID" width="200" prop="id" />
      <el-table-column label="费率配置名称" min-width="250" prop="feeName" show-overflow-tooltip />
      <el-table-column align="center" label="收入费率百分比" width="200" prop="incomeFeeRate" />
      <el-table-column align="center" label="收入固定费用金额(分)" width="200" prop="incomeFeeFixed" />
      <el-table-column align="center" label="支出费率百分比" width="200" prop="expenseFeeRate" />
      <el-table-column align="center" label="支出固定费用金额(分)" width="200" prop="expenseFeeFixed" />
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button v-hasPermi="['supplier:fee-config:update']" link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button v-hasPermi="['supplier:fee-config:delete']" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <FeeConfigForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import * as SupplierApi from '@/api/supplier'
import FeeConfigForm from './FeeConfigForm.vue'

defineOptions({ name: 'FeeConfig' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  feeName: undefined // 费率配置名称
})
const paginationData = ref({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
// /** 内容封面预览 */
// const imagePreview = (imgUrl: string) => {
//   createImageViewer({
//     urlList: [imgUrl]
//   })
// }
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.value.pageNum,
      pageSize: paginationData.value.pageSize,
      param: {
        feeName: queryParams.feeName || undefined
      }
    }
    console.log(184, params)
    const data = await SupplierApi.feeConfigList(params)
    console.log(109, data)
    list.value = data.pageContents || []
    paginationData.value.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  paginationData.value.pageNum = 1
  paginationData.value.pageTotal = 0
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, row?: object) => {
  formRef.value.open(type, row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SupplierApi.deleteFeeConfig(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// const categoryList = ref<SupplierApi.ArticleCategoryVO[]>([])
// const spuList = ref<ProductSpuApi.Spu[]>([])
onMounted(async () => {
  await getList()
})
</script>
