<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :align-center="true" custom-class="relation-dialog" width="500px">
    <ContentWrap :bottom="false" :border="false">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="匹配度" prop="threshold">
          <el-input-number v-model="queryParams.threshold" :min="0" :max="1" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />匹配</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />重置</el-button>
        </el-form-item>
      </el-form>
      <el-table stripe border highlight-current-row style="width: 100%" height="570" v-loading="loading" :data="tableData" @current-change="handleCurrentChange">
        <el-table-column label="属性名称" prop="text" show-overflow-tooltip />
        <el-table-column label="匹配度" prop="similarity" width="200" />
      </el-table>
    </ContentWrap>
  </Dialog>
</template>
<script lang="ts" setup>
import {semanticMatch} from "@/api/product/categoryMapping";


defineOptions({ name: 'SystemProductForm' })

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('属性匹配') // 弹窗的标题

const queryFormRef = ref() // 表单 Ref
const tableData = ref([])

const queryParams = reactive({
  query: '',
  candidates: [],
  threshold: 0.8
})

const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    const data = await semanticMatch(queryParams)
    console.log(data)
    tableData.value = data || []
  } finally {
    loading.value = false
  }
}
/** 打开弹窗 */
const open = async (query: string, candidates: []) => {
  dialogVisible.value = true
  queryParams.query = query
  queryParams.candidates = candidates
  queryParams.threshold = 0.8
  getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const handleCurrentChange = (val: object) => {
  emit("success", val.text)
  dialogVisible.value = false
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
</script>
<style lang="scss">
.divider{
  width: 100%;
  height: 1px;
  margin: 10px 0;
  background: #dcdfe6;
}
.el-overlay {
  .el-overlay-dialog{
    .relation-dialog{
      .el-dialog__body{
        padding: 0 !important;
      }
    }
  }
}
</style>
