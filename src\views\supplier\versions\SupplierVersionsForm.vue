<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!--id: undefined,-->
      <!--status: 0, // 上线状态-->
      <!--description: undefined // 版本描述-->
      <el-form-item label="版本名称" prop="versionName">
        <el-input v-model="formData.versionName" placeholder="请输入版本名称" />
      </el-form-item>
      <el-form-item label="版本编码" prop="versionCode">
        <el-input v-model="formData.versionCode" :disabled="formData.id" placeholder="请输入版本编码" />
      </el-form-item>
      <!--<el-form-item label="状态" prop="status">-->
      <!--  <el-radio-group v-model="formData.status">-->
      <!--    <el-radio-->
      <!--      v-for="dict in getIntDictOptions(DICT_TYPE.PUBLISHED_STATUS)"-->
      <!--      :key="dict.value"-->
      <!--      :value="dict.value"-->
      <!--    >-->
      <!--      {{ dict.label }}-->
      <!--    </el-radio>-->
      <!--  </el-radio-group>-->
      <!--</el-form-item>-->
      <el-form-item label="版本描述" prop="description">
        <el-input v-model="formData.description" placeholder="请输入版本描述" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
// import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as SupplierApi from '@/api/supplier'

defineOptions({ name: 'SupplierVersionsForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  versionCode: undefined, // 版本编码
  versionName: undefined, // 版本名称
  status: 0, // 上线状态
  description: undefined // 版本描述
})
const formRules = reactive({
  versionName: [{ required: true, message: '版本名称不能为空', trigger: 'blur' }],
  versionCode: [{ required: true, message: '版本编码不能为空', trigger: 'blur' }],
  // status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
  // description: [{ required: true, message: '版本描述不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SupplierApi.getSupplierVersion(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SupplierApi.SupplierVersionVO
    if (formType.value === 'create') {
      await SupplierApi.createSupplierVersion(data)
      message.success(t('common.createSuccess'))
    } else {
      await SupplierApi.updateSupplierVersion(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    versionCode: undefined, // 版本编码
    versionName: undefined, // 版本名称
    status: 0, // 上线状态
    description: undefined // 版本描述
  }
  formRef.value?.resetFields()
}
</script>
