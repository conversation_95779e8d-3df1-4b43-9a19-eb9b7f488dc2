<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="类型名称" prop="typeName">
        <el-input v-model="formData.typeName" placeholder="请输入类型名称" />
      </el-form-item>
      <el-form-item label="类型编号" prop="code">
        <el-input v-model="formData.code" :disabled="formData.id" placeholder="请输入类型编号" />
      </el-form-item>
      <el-form-item label="状态" prop="isVisible">
        <el-radio-group v-model="formData.isVisible">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.VISIBLE_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number v-model="formData.sortOrder" :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item label="类型介绍" prop="description">
        <el-input v-model="formData.description" placeholder="请输入类型介绍" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as ArticleApi from '@/api/article'

defineOptions({ name: 'PromotionArticleCategoryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  typeName: undefined,
  picUrl: undefined,
  isVisible: undefined,
  sortOrder: undefined,
  description: undefined
})
const formRules = reactive({
  typeName: [{ required: true, message: '类型名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '类型编号不能为空', trigger: 'blur' }],
  isVisible: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ArticleApi.getArticleCategory(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ArticleApi.ArticleCategoryVO
    if (formType.value === 'create') {
      await ArticleApi.createArticleCategory(data)
      message.success(t('common.createSuccess'))
    } else {
      await ArticleApi.updateArticleCategory(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    typeName: undefined,
    picUrl: undefined,
    isVisible: undefined,
    sortOrder: undefined,
    description: undefined
  }
  formRef.value?.resetFields()
}
</script>
