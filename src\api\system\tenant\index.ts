import request from '@/config/axios'

export interface TenantVO {
  id: number
  name: string
  contactName: string
  contactMobile: string
  status: number
  domain: string
  packageId: number
  username: string
  password: string
  expireTime: Date
  accountCount: number
  created: Date
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  created?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  created?: Date[]
}

// 获取企业分页列表
export const getTenantPage = (data) => {
  return request.post({ url: '/v1/platform/infra/tenant/page', data })
}

// 根据ID获取企业详细信息
export const getTenant = (id: number) => {
  return request.post({ url: '/v1/platform/infra/tenant/get?id=' + id })
}

// 获取企业精简信息列表
export const getTenantList = () => {
  return request.get({ url: '/v1/platform/infra/tenant/simple-list' })
}

// 新增企业
export const createTenant = (data: TenantVO) => {
  return request.post({ url: '/v1/platform/infra/tenant/create', data })
}

// 修改企业
export const updateTenant = (data: TenantVO) => {
  return request.put({ url: '/v1/platform/infra/tenant/update', data })
}

// 删除企业
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/v1/platform/infra/tenant/delete?id=' + id })
}

// 导出企业
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/v1/platform/infra/tenant/export-excel', params })
}
