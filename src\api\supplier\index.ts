import request from '@/config/axios'
import { PermissionAssignRoleMenuReqVO } from '@/api/system/permission'

export interface SupplierVersionVO {
  id?: number, // 版本ID
  versionCode: string, // 版本编码
  versionName: string, // 版本名称
  status: number, // 上线状态
  description: string // 版本描述
  created?: Date // 创建时间
}

export interface SupplierVO {
  id: number
  name: string
  code: string
  sort: number
  status: number
  type: number
  dataScope: number
  dataScopeDeptIds: number[]
  created: Date
}
export interface FeeConfigVO {
  id: number
  feeName: string
  incomeFeeRate: number
  incomeFeeFixed: number
  expenseFeeRate: number
  expenseFeeFixed: number
}

// 创建版本
export const createSupplierVersion = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/version/create', data })
}

// 获取版本分页列表
export const supplierVersionPage = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/version/page', data })
}

// 删除版本
export const deleteSupplierVersion = async (id: number) => {
  return await request.delete({ url: `/v1/platform/infra/version/delete?id=${id}` })
}

// 获取版本详情
export const getSupplierVersion = async (id: number) => {
  return await request.get({ url: '/v1/platform/infra/version/get?id=' + id })
}

// 获取所有版本简单列表
export const getSupplierVersionAll = async () => {
  return await request.get({ url: '/v1/platform/infra/version/list-all' })
}
// 更新活动信息
export const updateSupplierVersion = async (data:SupplierVersionVO) => {
  return await request.put({ url: `/v1/platform/infra/version/update`, data })
}

// 获取版本的菜单ID列表
export const getVersionMenuIds = async (versionId: number) => {
  return await request.get({ url: '/v1/platform/infra/version/menu-ids?versionId=' + versionId })
}

// 为版本分配菜单
export const assignVersionMenu = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/version/assign-menu', data })
}

// 获取企业关联的费率配置
export const getEnterpriseversion = async (enterpriseId: number) => {
  return await request.get({ url: `/v1/platform/infra/supplier-account/get-version-info?enterpriseId=${enterpriseId}` })
}

// 更换企业版本
export const updateEnterpriseversion = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/supplier-account/update-version', data })
}

// 查询供应商列表
export const getSupplierPage = async (data) => {
  return await request.post({ url: '/v1/platform/infra/supplier-account/page', data })
}

// 获取企业下的账户分页列表
export const getSupplierAccountPage = async (data) => {
  return await request.post({ url: '/v1/platform/infra/supplier-account/enterprise-account/page', data })
}

// 查询供应商（精简)列表
export const getSimpleSupplierList = async (): Promise<SupplierVO[]> => {
  return await request.get({ url: '/v1/platform/infra/role/simple-list' })
}

// 查询供应商详情
export const getSupplier = async (id: number) => {
  return await request.get({ url: '/v1/platform/infra/role/get?id=' + id })
}

// 导出供应商
export const exportSupplier = (params) => {
  return request.download({
    url: '/v1/platform/infra/role/export-excel',
    params
  })
}

// 查询供应商拥有的菜单权限
export const getRoleMenuList = async (roleId: number) => {
  return await request.get({ url: `/v1/platform/infra/permission/list-role-menus?roleId=${roleId}` })
}

// 赋予供应商菜单权限
export const assignRoleMenu = async (data: PermissionAssignRoleMenuReqVO) => {
  return await request.post({ url: '/v1/platform/infra/permission/assign-role-menu', data })
}

// 创建费率配置
export const createFeeConfig = async (data:object) => {
  return await request.post({ url: '/v1/platform/trade/fee-config', data })
}

// 更新费率配置
export const updateFeeConfig = (data: object) => {
  return request.post({ url: '/v1/platform/trade/fee-config/edit', data })
}

// 删除费率配置
export const deleteFeeConfig = (id: number) => {
  return request.get({ url: `/v1/platform/trade/fee-config/remove/${id}` })
}

// 根据ID获取费率配置
export const getFeeConfig = async (id: number) => {
  return await request.get({ url: `/v1/platform/trade/fee-config/${id}` })
}

// 获取所有费率配置列表
export const getFeeConfigList = async () => {
  return await request.get({ url: '/v1/platform/trade/fee-config/list' })
}

// 获取所有费率配置列表
export const feeConfigList = async (data: object) => {
  return await request.post({ url: '/v1/platform/trade/fee-config/page', data })
}

// 设置企业费率关联
export const setFeeConfig = async (data: object) => {
  return await request.post({ url: '/v1/platform/trade/fee-config/rel', data })
}

// 解除企业费率关联
export const cancelFeeConfig = async (enterpriseId: number) => {
  return await request.get({ url: `/v1/platform/trade/fee-config/cancel-rel/${enterpriseId}` })
}

// 获取企业关联的费率配置
export const getEnterpriseFeeConfig = async (enterpriseId: number) => {
  return await request.get({ url: `/v1/platform/trade/fee-config/enterprise/${enterpriseId}` })
}

// 获取当前登录企业关联的费率配置
export const getCurrentEnterpriseFeeConfig = async () => {
  return await request.get({ url: '/v1/platform/trade/fee-config/current-enterprise' })
}
