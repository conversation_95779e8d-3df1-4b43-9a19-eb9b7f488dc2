# 开发环境：本地只启动前端项目，依赖开发环境（后端、APP）
NODE_ENV=production

VITE_DEV=true

# 请求路径
VITE_BASE_URL='https://test-api-ecommate.yaotown.com'
#VITE_BASE_URL='http://192.168.10.99:48080'

# 接口地址
VITE_API_URL=''

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist

# 租户开关
VITE_APP_TENANT_ENABLE=false

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'