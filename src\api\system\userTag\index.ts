import request from '@/config/axios'

export interface TagVO {
  accountTagId: number | undefined // 用户标签ID
  tagName: string | undefined // 标签名称
  gender: number | undefined // 性别
  memberLevel?: [] | undefined // 会员等级
  regionType?: number | undefined // 地区条件类型。0：全部地区；1：指定地区
  region?: string | undefined // 地区
  registerTime?: number | undefined // 注册时间
  registerStart: string | null // 注册开始时间
  registerEnd: string | null // 注册结束时间
  registerDays: string | null // 注册天数大于
  orderCountCondition?: number | undefined // 成交笔数条件
  orderCount: string | null // 成交笔数大于
  orderCountStartTime: string | null // 成交笔数统计开始时间
  orderCountEndTime: string | null // 成交笔数统计结束时间
  orderAmountCondition?: number | undefined // 成交笔数条件
  orderAmount: string | null // 成交金额大于
  orderAmountStartTime: string | null // 成交金额统计开始时间
  orderAmountEndTime: string | null // 成交金额统计结束时间
}

// 获取标签列表
export const getTagPage = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/account-tag/page', data })
}

// 获取标签详情
export const getTag = async (id: number) => {
  return await request.get({ url: '/v1/platform/infra/account-tag/detail?id=' + id })
}

// 创建标签
export const createTag = async (data: TagVO) => {
  return await request.post({ url: '/v1/platform/infra/account-tag/create', data })
}

// 修改标签
export const updateTag = async (data: TagVO) => {
  return await request.post({ url: '/v1/platform/infra/account-tag/update', data })
}

// 删除标签
export const deleteTag = async (id: number) => {
  return await request.post({ url: '/v1/platform/infra/account-tag/delete?id=' + id })
}


// 分页查询标签关联的用户信息
export const accountTagUsers = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/account-tag/users', data })
}
