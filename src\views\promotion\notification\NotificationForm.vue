<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
    >
      <el-form-item label="消息类型" prop="messageType">
        <el-select v-model="formData.messageType" placeholder="请选择消息类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.MESSAGE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="消息标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入消息标题" />
      </el-form-item>
      <el-form-item label="消息内容" prop="content">
        <el-input type="textarea" v-model="formData.content" placeholder="请输入消息内容" />
      </el-form-item>
      <el-form-item label="跳转类型" prop="jumpType">
        <el-radio-group v-model="formData.jumpType">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.MESSAGE_JUMP_TYPE)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!--<template v-if="formData.jumpType || formData.jumpType !== 0">-->
      <!--  -->
      <!--</template>-->
      <el-form-item v-if="formData.jumpType === 0" label="跳转内容" prop="jumpContent">
        <Editor v-model="formData.jumpDetail" height="200px" />
      </el-form-item>
      <el-form-item v-if="[1, 2].includes(formData.jumpType)" label="跳转链接" prop="jumpPageType">
        <div :style="{ display: 'block'}">
          <div>
            <el-radio-group v-model="formData.jumpPageType">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.JUMP_LINK_TYPE)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </div>
          <div>
            <el-input v-model="formData.jumpPage" placeholder="请输入" />
          </div>
        </div>

        <el-text v-if="formData.jumpPageType === 1" class="w-full" size="small" type="info"> 内链与外链时填写，富文本不用填写</el-text>
      </el-form-item>
      <el-form-item label="封面图" prop="coverImages">
        <UploadImgs v-model="formData.coverImages" height="80px" width="80px" listType="picture-card" />
        <el-text class="w-full" size="small" type="info">只支持.jpg .png 格式，最多5张</el-text>
      </el-form-item>
      <el-form-item label="推送位置" prop="pushPosition">
        <el-checkbox-group v-model="formData.pushPosition">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.PUSH_LOCATION)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="推送会员" prop="pushMembers">
        <el-checkbox-group v-model="formData.pushMembers">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.MEMBER_LEVEL_OPTION)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="推送标签" prop="accountTagIds">
        <div :style="{ display: 'block'}">
          <!--<div>-->
          <!--  <el-button type="primary" link >新增/查看标签</el-button>-->
          <!--</div>-->
          <el-checkbox-group v-model="formData.accountTagIds">
            <el-checkbox
              v-for="tag in tagList"
              :key="tag.accountTagId"
              :value="tag.accountTagId"
            >
              {{ tag.tagName }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item label="推送时间" prop="pushType">
        <el-radio-group v-model="formData.pushType">
          <div :style="{ width: '100%' }">
            <el-radio :value="0">
              立即推送
            </el-radio>
          </div>
          <div :style="{ width: '100%' }">
            <el-radio :value="1">
              定时推送
              <el-date-picker v-model="formData.scheduledTime" type="date" value-format="YYYY-MM-DD 00:00:00" size="small" placeholder="选择推送时间" />
            </el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否推送" prop="isEnabled">
        <el-radio-group v-model="formData.isEnabled">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!--<el-form-item label="备注" prop="remark">-->
      <!--  <el-input v-model="formData.remark" placeholder="请输入备注" />-->
      <!--</el-form-item>-->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as NotifyTemplateApi from '@/api/system/notify/template'
const message = useMessage() // 消息弹窗
import * as UserTagApi from '@/api/system/userTag'

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型
const formData = ref<NotifyTemplateApi.NotifyTemplateVO>({
  id: '', // 消息ID
  title: "", // 消息标题
  content: "", // 消息内容
  messageType: "", // 消息类型
  jumpType: 0, // 跳转类型：0-富文本，1-内链，2-外链
  jumpPageType: 0, // 跳转页面类型：0-自定义，1-活动，2-订单，3-商品，4-优惠劵
  jumpDetail: "", // 跳转富文本--内容
  jumpPage: "", // 跳转页面
  coverImages: [], // 封面图URL，多个以逗号分隔
  pushPosition: [], // 推送位置
  pushMembers: [], // 推送会员
  accountTagIds: [], // 标签ID列表，当tagType为SELECTED时使用
  pushType: 0, // 推送类型：0-立即推送，1-定时推送
  pushTime: "", // 推送时间，当pushType为1时使用
  scheduledTime: "", // 定时推送时间，当pushType为1时使用
  isEnabled: 0 // 是否启用：0-禁用，1-启用

})
const formRules = reactive({
  type: [{ required: true, message: '消息类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '消息编码不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '消息名称不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '发件人姓名不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '消息内容不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const tagList = ref([])
const getList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      param: {}
    }
    const data = await UserTagApi.getTagPage(params)
    tagList.value = data.pageContents || []
  } finally {
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  console.log(204, type, id)
  dialogVisible.value = true
  dialogTitle.value = type
  formType.value = type
  resetForm()
  getList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      console.log(213)
      const obj = await NotifyTemplateApi.getNotifyTemplate(id)
      console.log(215, obj)
      obj.jumpDetail = obj.jumpDetail || ''
      obj.coverImages = obj.coverImages ? obj.coverImages.split(',') : []
      obj.pushPosition = obj.pushPosition ? obj.pushPosition.split(',').map(Number) : undefined
      obj.pushMembers = obj.pushMembers ? obj.pushMembers.split(',').map(Number) : undefined
      console.log(217, obj)
      formData.value = obj
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  formLoading.value = true
  try {
    const data = {
      ...formData.value,
      coverImages: formData.value.coverImages?.join(',') || null,
      pushPosition: formData.value.pushPosition?.join(',') || null,
      pushMembers: formData.value.pushMembers?.join(',') || null
    }
    if (formType.value === 'create') {
      await NotifyTemplateApi.createNotifyTemplate(data)
      message.success('新增成功')
    } else {
      await NotifyTemplateApi.updateNotifyTemplate(data)
      message.success('修改成功')
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: '', // 消息ID
    title: "", // 消息标题
    content: "", // 消息内容
    messageType: "", // 消息类型
    jumpType: 0, // 跳转类型：0-富文本，1-内链，2-外链
    jumpPageType: 0, // 跳转页面类型：0-自定义，1-活动，2-订单，3-商品，4-优惠劵
    jumpDetail: "", // 跳转富文本--内容
    jumpPage: "", // 跳转页面
    coverImages: [], // 封面图URL，多个以逗号分隔
    pushPosition: [], // 推送位置
    pushMembers: [], // 推送会员
    accountTagIds: [], // 标签ID列表，当tagType为SELECTED时使用
    pushType: 0, // 推送类型：0-立即推送，1-定时推送
    pushTime: "", // 推送时间，当pushType为1时使用
    scheduledTime: "", // 定时推送时间，当pushType为1时使用
    isEnabled: 0 // 是否启用：0-禁用，1-启用
  }
  formRef.value?.resetFields()
}
</script>
