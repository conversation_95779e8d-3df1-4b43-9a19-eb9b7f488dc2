<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="位置名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入位置名称" />
      </el-form-item>
      <el-form-item label="位置编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入位置编码" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item label="是否显示" prop="isShow">
        <el-radio-group v-model="formData.isShow">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.VISIBLE_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="位置图标" prop="icon">
        <UploadImg v-model="formData.icon" height="80px" />
      </el-form-item>
      <el-form-item label="位置介绍" prop="intro">
        <el-input v-model="formData.intro" type="textarea" placeholder="请输入位置介绍" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as AdvertisingApi from '@/api/promotion/advertising'
import { VisibleStatusEnum } from '@/utils/constants'

defineOptions({ name: 'PromotionAdvertisingCategoryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的位置：create - 新增；update - 修改
const formData = ref({
  adPositionId: undefined,
  name: undefined,
  code: undefined,
  icon: undefined,
  isShow: VisibleStatusEnum.VISIBLE,
  sort: undefined,
  intro: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '位置名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '位置编码不能为空', trigger: 'blur' }],
  isShow: [{ required: true, message: '显示状态不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AdvertisingApi.getAdvertisingCategory(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AdvertisingApi.AdvertisingCategoryVO
    if (formType.value === 'create') {
      await AdvertisingApi.createAdvertisingCategory(data)
      message.success(t('common.createSuccess'))
    } else {
      await AdvertisingApi.updateAdvertisingCategory(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    adPositionId: undefined,
    name: undefined,
    code: undefined,
    icon: undefined,
    isShow: VisibleStatusEnum.VISIBLE,
    sort: undefined,
    intro: undefined
  }
  formRef.value?.resetFields()
}
</script>
