import request from '@/config/axios'

export interface AdvertisingVO {
  id: number
  categoryId: number
  title: string
  author: string
  picUrl: string
  introduction: string
  browseCount: string
  sort: number
  status: number
  spuId: number
  recommendHot: boolean
  recommendBanner: boolean
  content: string
}

export interface AdvertisingCategoryVO {
  id: number
  name: string
  picUrl: string
  status: number
  sort: number
}
export interface AdvertisingCategoryListVO {
  adPositionId: number
  name: string
  code: string
}

// 查询广告位管理列表
export const getAdvertisingPage = async (data: object) => {
  return await request.post({ url: `/v1/platform/infra/advertising/place/page`, data })
}

// 查询广告位管理详情
export const getAdvertising = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/advertising/place/get?id=` + id })
}

// 新增广告
export const createAdvertising = async (data: AdvertisingVO) => {
  return await request.post({ url: `/v1/platform/infra/advertising/place/create`, data })
}

// 修改广告
export const updateAdvertising = async (data: AdvertisingVO) => {
  return await request.put({ url: `/v1/platform/infra/advertising/place/update`, data })
}

// 删除广告
export const deleteAdvertising = async (id: number) => {
  return await request.delete({ url: `/v1/platform/infra/advertising/place/delete?id=` + id })
}


// 查询广告位置列表
export const getAdvertisingCategoryPage = async (data) => {
  return await request.post({ url: `/v1/platform/infra/advertising/position/page`, data })
}

// 查询广告位置精简信息列表
export const getSimpleAdvertisingCategoryList = async () => {
  return await request.get({ url: `/v1/platform/infra/advertising/place/position/list` })
}

// 查询广告位置详情
export const getAdvertisingCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/advertising/position/get?id=` + id })
}

// 新增广告位置
export const createAdvertisingCategory = async (data: AdvertisingCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/advertising/position/create`, data })
}

// 修改广告位置
export const updateAdvertisingCategory = async (data: AdvertisingCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/advertising/position/update`, data })
}

// 删除广告位置
export const deleteAdvertisingCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/advertising/position/delete?id=` + id })
}
