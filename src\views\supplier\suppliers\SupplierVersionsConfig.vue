<template>
  <Dialog v-model="dialogVisible" title="费率配置" width="400">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="版本" prop="versionId">
        <el-select v-model="formData.versionId" clearable placeholder="请选择版本">
          <el-option
            v-for="dict in categoryList"
            :key="dict.id"
            :label="dict.versionName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="同步菜单" prop="syncMenus">
        <el-radio-group v-model="formData.syncMenus">
          <el-radio :value="false">否</el-radio>
          <el-radio :value="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as SupplierApi from '@/api/supplier'

defineOptions({ name: 'SupplierVersionsConfig' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  versionId: null,
  enterpriseId: 0,
  syncMenus: false
})
const formRef = ref() // 表单 Ref
const formRules = reactive({
  versionId: [{ required: true, message: '供应商版本不能为空', trigger: 'change' }]
})

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const obj = await SupplierApi.getEnterpriseversion(id)
      if (obj && obj.id) {
        formData.value = {
          ...obj,
          enterpriseId: id,
          versionId: obj.id,
          syncMenus: false
        }
      } else {
        formData.value = {
          enterpriseId: id,
          versionId: null,
          syncMenus: false
        }
      }
      formLoading.value = false
    } finally {
      formLoading.value = false
    }
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    versionId: null,
    enterpriseId: 0,
    syncMenus: false
  }
  formRef.value?.resetFields()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const param = {
      versionId: formData.value.versionId,
      enterpriseId: formData.value.enterpriseId,
      syncMenus: formData.value.syncMenus
    }
    await SupplierApi.updateEnterpriseversion(param)
    message.success('设置成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const categoryList = ref<SupplierApi.SupplierVersionVO[]>([])
onMounted(async () => {
  // 加载配置
  categoryList.value = await SupplierApi.getSupplierVersionAll()
  console.log(108, categoryList.value)
})
</script>
