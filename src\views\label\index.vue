<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="标签名称" prop="tagName">
        <el-input v-model="queryParams.tagName" class="!w-240px" clearable placeholder="请输入标签名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="tagState">
        <el-select v-model="queryParams.tagState" class="!w-240px" clearable placeholder="请选择标签状态">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['tag:create']" plain type="primary" @click="openForm('create')">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
        <el-button v-hasPermi="['tag:export']" :loading="exportLoading" plain type="success" @click="handleExport">
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" row-key="id">
      <el-table-column align="center" label="标签编号" prop="id" />
      <el-table-column align="center" label="标签名称" prop="tagName" show-overflow-tooltip />
      <el-table-column align="center" label="标签排序" prop="tagSort" width="120" />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button v-hasPermi="['tag:update']" link type="primary" @click="openForm('update', scope.row.id)">修改</el-button>
          <router-link :to="'/label/product/' + scope.row.id">
            <el-button link type="primary">商品</el-button>
          </router-link>
          <router-link :to="'/label/category/' + scope.row.id">
            <el-button link type="primary">类目</el-button>
          </router-link>
          <el-button v-hasPermi="['tag:delete']" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProductTagForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TagApi from '@/api/tag'
import ProductTagForm from './ProductTagForm.vue'
import download from '@/utils/download'

defineOptions({ name: 'TagList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 标签表格数据
const queryParams = reactive({
  tagName: '',
  tagState: undefined
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询标签类型列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        tagName: queryParams.tagName || undefined,
        tagState: queryParams.status
      }
    }
    const data = await TagApi.getProductTagPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TagApi.deleteProductTag({ tagIds: [id] })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TagApi.exportProductTag(queryParams)
    download.excel(data, '标签类型.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
