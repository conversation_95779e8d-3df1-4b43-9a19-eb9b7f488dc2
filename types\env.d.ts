/// <reference types="vite/client" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string // 系统标题
  readonly VITE_PORT: number // 项目本地运行端口号
  readonly VITE_OPEN: string // open 运行 npm run dev 时自动打开浏览器
  readonly VITE_DEV: string //
  readonly VITE_APP_CAPTCHA_ENABLE: string // 验证码开关
  readonly VITE_APP_TENANT_ENABLE: string // 租户开关
  readonly VITE_BASE_URL: string // 请求路径
  readonly VITE_API_URL: string // 接口地址
  readonly VITE_BASE_PATH: string // 打包路径
  readonly VITE_DROP_DEBUGGER: string // 是否删除debugger
  readonly VITE_DROP_CONSOLE: string // 是否删除console.log
  readonly VITE_SOURCEMAP: string // 是否sourcemap
  readonly VITE_OUT_DIR: string // 输出路径
  readonly VITE_GOVIEW_URL: string // GoView域名
}

declare global {
  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}
