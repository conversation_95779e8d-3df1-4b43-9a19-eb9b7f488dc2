<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :fullscreen="true">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="账户" name="account">
        <el-form
          ref="queryFormRef"
          :inline="true"
          :model="queryParams"
          class="-mb-15px"
        >
          <el-form-item label="账户名称" prop="name">
            <el-input v-model="queryParams.name" class="!w-240px" clearable placeholder="请输入账户名称" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="queryParams.phone" class="!w-240px" clearable placeholder="请输入手机号" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="账户类型" prop="accountType">
            <el-select v-model="queryParams.accountType" class="!w-240px" clearable placeholder="请选择账户类型">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ACCOUNT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery">
              <Icon class="mr-5px" icon="ep:search" />
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon class="mr-5px" icon="ep:refresh" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="list" :border="true">
          <el-table-column label="账户ID" align="center" prop="id" width="200" />
          <el-table-column label="账户名称" align="center" prop="name" />
          <el-table-column label="手机号" align="center" prop="phone" width="150" />
          <el-table-column label="账户类型" align="center" prop="accountType" width="120">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.ACCOUNT_TYPE" :value="scope.row.accountType" />
            </template>
          </el-table-column>
          <el-table-column label="企业账户类型" align="center" prop="enterpriseAccountType" width="120">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.ENTERPRISE_ACCOUNT_TYPE" :value="scope.row.enterpriseAccountType" />
            </template>
          </el-table-column>
          <el-table-column label="生效日期" align="center" prop="effectiveDate" width="180" :formatter="dateFormatter2" />
          <el-table-column label="失效日期" align="center" prop="expiryDate" width="180" :formatter="dateFormatter2" />
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="paginationData.pageTotal"
          v-model:page="paginationData.pageNum"
          v-model:limit="paginationData.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </Dialog>
</template>
<script lang="ts" setup>
import type { TabsPaneContext } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as SupplierApi from '@/api/supplier'
import { dateFormatter2 } from '@/utils/formatTime'

defineOptions({ name: 'SupplierForm' })

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题

const activeName = ref('account')
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

const loading = ref(false) // 列表的加载中
const list = ref([])
const queryParams = reactive({
  name: '',
  phone: '',
  accountType: undefined,
})
const queryFormRef = ref() // 搜索的表单
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const enterpriseId = ref()
/** 打开弹窗 */
const open = async (id: number) => {
  dialogTitle.value = '供应商详情'
  dialogVisible.value = true
  enterpriseId.value = id
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        enterpriseId: enterpriseId.value || undefined,
        name: queryParams.name || undefined,
        phone: queryParams.phone || undefined,
        accountType: !queryParams.accountType && queryParams.accountType !== 0 ? undefined : queryParams.accountType
      }
    }
    const data = await SupplierApi.getSupplierAccountPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

</script>
