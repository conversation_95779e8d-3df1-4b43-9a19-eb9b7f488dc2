<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="反馈code" prop="code">
        <el-input
          v-model="queryParams.code"
          class="!w-240px"
          clearable
          placeholder="请输入反馈code"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否回复" prop="replyFlag">
        <el-select v-model="queryParams.replyFlag" class="!w-240px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.REPLY_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提问时间" prop="created">
        <el-date-picker
          v-model="queryParams.created"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <!--<el-button-->
        <!--  v-hasPermi="['promotion:feedback:create']"-->
        <!--  plain-->
        <!--  type="primary"-->
        <!--  @click="openForm('create')"-->
        <!--&gt;-->
        <!--  <Icon class="mr-5px" icon="ep:plus" />-->
        <!--  新增-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :show-overflow-tooltip="true" :stripe="true">
      <el-table-column align="center" label="id" min-width="180" prop="id" show-overflow-tooltip />
      <el-table-column align="center" label="问题类型" prop="feedbackTypeName" min-width="100"/>
      <el-table-column :formatter="dateFormatter" align="center" label="提问时间" prop="questionTime" width="160px" />
      <el-table-column align="center" label="反馈内容" min-width="300" prop="content" show-overflow-tooltip />
      <el-table-column align="center" label="反馈人姓名" prop="userName" min-width="100" />
      <el-table-column align="center" label="反馈人电话" prop="userPhone" min-width="120" />
      <el-table-column align="center" label="是否回复" width="100" prop="replyFlag">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.REPLY_STATUS" :value="scope.row.replyFlag" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="处理人" prop="handlerName" min-width="100" />
      <el-table-column :formatter="dateFormatter" align="center" label="回复时间" prop="replyTime" width="160px" />
      <el-table-column align="center" label="回复内容" min-width="300" prop="replyContent" show-overflow-tooltip />
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button v-hasPermi="['promotion:feedback:reply']" link type="primary" @click="openReplyForm(scope.row.id)">
            回复
          </el-button>
          <el-button v-hasPermi="['promotion:feedback:update']" link type="primary" @click="openForm(scope.row.id)">
            详情
          </el-button>

          <!--<el-button v-hasPermi="['promotion:feedback:delete']" link type="danger" @click="handleDelete(scope.row.id)">-->
          <!--  删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <FeedbackForm ref="formRef" @success="getList" />
  <!--回复弹框-->
  <FeedbackReplyForm ref="replyFormRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as FeedbackApi from '@/api/promotion/feedback'
import FeedbackForm from './FeedbackForm.vue'
import FeedbackReplyForm from './FeedbackReplyForm.vue'

defineOptions({ name: 'PromotionFeedback' })

// const message = useMessage() // 消息弹窗
// const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  code: undefined,
  replyFlag: undefined,
  startTime: undefined,
  endTime: undefined,
  created: []
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
// /** 反馈封面预览 */
// const imagePreview = (imgUrl: string) => {
//   createImageViewer({
//     urlList: [imgUrl]
//   })
// }
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        code: queryParams.code || undefined,
        replyFlag: queryParams.replyFlag,
        startTime: queryParams.created[0] || undefined,
        endTime: queryParams.created[1] || undefined
      }
    }
    const data = await FeedbackApi.getFeedbackPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (id: number) => {
  formRef.value.open(id)
}
/** 添加/修改操作 */
const replyFormRef = ref()
const openReplyForm = (id?: number) => {
  replyFormRef.value.open(id)
}

// /** 删除按钮操作 */
// const handleDelete = async (id: number) => {
//   try {
//     // 删除的二次确认
//     await message.delConfirm()
//     // 发起删除
//     await FeedbackApi.deleteFeedback(id)
//     message.success(t('common.delSuccess'))
//     // 刷新列表
//     await getList()
//   } catch {}
// }

const categoryList = ref<FeedbackApi.FeedbackCategoryVO[]>([])
onMounted(async () => {
  await getList()
  // 加载分类、商品列表
  categoryList.value =
    (await FeedbackApi.getSimpleFeedbackCategoryList()) as FeedbackApi.FeedbackCategoryVO[]
})
</script>
