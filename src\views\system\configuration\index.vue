<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="配置键名" prop="configKey">
        <el-input v-model="queryParams.configKey" class="!w-240px" clearable placeholder="请输入配置键名" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="配置名称" prop="configName">
        <el-input v-model="queryParams.configName" class="!w-240px" clearable placeholder="请输入配置名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="配置分组" prop="configName">
        <el-input v-model="queryParams.configName" class="!w-240px" clearable placeholder="请输入配置分组" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="配置类型" prop="type">
        <el-select
          v-model="queryParams.type"
          class="!w-160px"
          clearable
          placeholder="请选择配置类型"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_CONFIG_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['system:configuration:create']" plain type="primary" @click="openForm('create')">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
        <!--<el-button v-hasPermi="['system:configuration:export']" :loading="exportLoading" plain type="success" @click="handleExport">-->
        <!--  <Icon class="mr-5px" icon="ep:download" />-->
        <!--  导出-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" row-key="id">
      <el-table-column align="center" label="id" prop="id" width="150" />
      <el-table-column align="center" label="参数分组" prop="category" width="200" show-overflow-tooltip />
      <el-table-column align="center" label="参数名称" prop="name" width="200" show-overflow-tooltip />
      <el-table-column align="center" label="参数键名" prop="configKey" width="200" />
      <el-table-column align="center" label="参数键值" prop="value" min-width="200" show-overflow-tooltip />
      <el-table-column align="center" label="参数类型" prop="type" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_CONFIG_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否可见" prop="visible" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISIBLE_STATUS" :value="scope.row.visible" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark" min-width="200" show-overflow-tooltip />
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="scope">
          <el-button v-hasPermi="['system:configuration:update']" link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button v-hasPermi="['system:configuration:delete']" link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ConfigurationForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as ConfigurationApi from '@/api/system/configuration'
import ConfigurationForm from './ConfigurationForm.vue'
import download from '@/utils/download'

defineOptions({ name: 'SystemConfiguration' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 字典表格数据
const queryParams = reactive({
  configKey: '', // 配置键名
  configName: '', // 配置名称
  category: '', // 配置分组
  type: '', // 配置类型
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询字典类型列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        configKey: queryParams.configKey || undefined,
        configName: queryParams.configName || undefined,
        category: queryParams.category || undefined,
        type: queryParams.type || undefined
      }
    }
    const data = await ConfigurationApi.getSystemConfigPage(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ConfigurationApi.deleteSystemConfig(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ConfigurationApi.exportSystemConfig(queryParams)
    download.excel(data, '字典类型.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>






