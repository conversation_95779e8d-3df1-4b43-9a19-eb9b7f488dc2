<template>
  <Dialog :title="dialogTitle" :fullscreen="true" v-model="dialogVisible">
    <div class="chunk-title"> 标签信息 </div>
    <ContentWrap>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        :disabled="formType === 'view'"
        label-suffix="："
      >
        <el-form-item label="标签名称" prop="tagName">
          <el-input v-model="formData.tagName" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="自动打标签设置" />
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="formData.gender">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_SEX_OPTION)"
              :key="dict.value"
              :value="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="会员" prop="memberLevel">
          <el-checkbox-group v-model="formData.memberLevel">
            <el-checkbox v-for="dict in getIntDictOptions(DICT_TYPE.MEMBER_LEVEL_OPTION)" :key="dict.value" :value="dict.value" :label="dict.label" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="城市" prop="regionType">
          <div :style="{ display: 'block'}">
            <div>
              <el-radio-group v-model="formData.regionType">
                <el-radio :value="0">全部地区</el-radio>
                <el-radio :value="1">指定地区</el-radio>
              </el-radio-group>
            </div>
            <div v-if="formData.regionType === 1">
              <el-cascader
                v-model="formData.region"
                :options="areaTree"
                :props="defaultProps2"
                clearable
                placeholder="请选择地区"
                filterable
                class="!w-480px"
                :show-all-levels="false"
                collapse-tags
                :max-collapse-tags="10"
                collapse-tags-tooltip
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="注册时间" prop="registerTime">
          <el-radio-group v-model="formData.registerTime">
            <div :style="{ width: '100%' }">
              <el-radio :value="0">无限制</el-radio>
            </div>
            <div :style="{ width: '100%' }">
              <el-radio :value="1">
                <div>
                  日期范围
                  <el-date-picker v-model="formData.registerStart" type="date" value-format="YYYY-MM-DD 00:00:00" placeholder="请选择开始时间" size="small" /> -
                  <el-date-picker v-model="formData.registerEnd" type="date" value-format="YYYY-MM-DD 23:59:59" placeholder="请选择结束时间" size="small" />
                </div>
                <div>
                  固定天数
                  <el-input-number v-model="formData.registerDays" :min="1" :precision="0" :step="1" class="!w-120px" size="small">
                    <template #suffix>
                      <span>天</span>
                    </template>
                  </el-input-number>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="成交笔数" prop="orderCountCondition">
          <el-radio-group v-model="formData.orderCountCondition">
            <div :style="{ width: '100%' }">
              <el-radio :value="0">无限制</el-radio>
            </div>
            <div :style="{ width: '100%' }">
              <el-radio :value="1">
                日期范围
                <el-date-picker v-model="formData.orderCountStartTime" type="date" value-format="YYYY-MM-DD 00:00:00" placeholder="请选择开始时间" size="small" /> -
                <el-date-picker v-model="formData.orderCountEndTime" type="date" value-format="YYYY-MM-DD 23:59:59" placeholder="请选择结束时间" size="small" />
              </el-radio>
            </div>
            <div :style="{ width: '100%' }">
              <el-radio :value="2">
                累加成交笔数
                <el-input-number v-model="formData.orderCount" :min="1" :precision="0" :step="1" size="small" class="!w-120px">
                  <template #suffix>
                    <span>笔</span>
                  </template>
                </el-input-number>
              </el-radio>
            </div>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="成交金额" prop="orderAmountCondition">
          <el-radio-group v-model="formData.orderAmountCondition">
            <div :style="{ width: '100%' }">
              <el-radio :value="0">无限制</el-radio>
            </div>
            <div :style="{ width: '100%' }">
              <el-radio :value="1">
                日期范围
                <el-date-picker v-model="formData.orderAmountStartTime" type="date" value-format="YYYY-MM-DD 00:00:00" placeholder="请选择开始时间" size="small" /> -
                <el-date-picker v-model="formData.orderAmountEndTime" type="date" value-format="YYYY-MM-DD 23:59:59" placeholder="请选择结束时间" size="small" />
              </el-radio>
            </div>
            <div :style="{ width: '100%' }">
              <el-radio :value="2">
                累加成交金额
                <el-input-number
                  v-model="formData.orderAmount" :min="1" :precision="0" :step="1" size="small" class="!w-120px">
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </el-radio>
            </div>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <template #footer>
      <el-button v-if="formType !== 'view'" type="primary" :disabled="formLoading" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as UserTagApi from '@/api/system/userTag'
import * as AreaApi from '@/api/system/area'
import { defaultProps } from '@/utils/tree'

const message = useMessage() // 标签弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formDisabled = ref(false)
const formType = ref('') // 表单的类型
const formData = ref<UserTagApi.TagVO>({
  accountTagId: undefined, // 用户标签ID
  tagName: undefined, // 标签名称
  gender: 0, // 性别
  memberLevel: [], // 会员等级
  regionType: 0, // 地区条件类型。0：全部地区；1：指定地区
  region: undefined, // 地区
  registerTime: 0, // 注册时间
  registerStart: null, // 注册开始时间
  registerEnd: null, // 注册结束时间
  registerDays: null, // 注册天数大于
  orderCountCondition: 0, // 成交笔数条件
  orderCount: null, // 成交笔数大于
  orderCountStartTime: null, // 成交笔数统计开始时间
  orderCountEndTime: null, // 成交笔数统计结束时间
  orderAmountCondition: 0, // 成交笔数条件
  orderAmount: null, // 成交金额大于
  orderAmountStartTime: null, // 成交金额统计开始时间
  orderAmountEndTime: null // 成交金额统计结束时间
})
const areaTree = ref([])
const defaultProps2 = {
  ...defaultProps,
  multiple: true
}
const formRules = reactive({
  type: [{ required: true, message: '标签类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '标签编码不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '发件人姓名不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '标签内容不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

onMounted(async () => {
  // 加载区域数据
  areaTree.value = await AreaApi.getAreaTree()
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type
  switch (type) {
    case 'create':
      dialogTitle.value = '创建标签'
      break
    case 'view':
      dialogTitle.value = '查看标签'
      break
    case 'update':
      dialogTitle.value = '编辑标签'
      break
  }
  formType.value = type
  formDisabled.value = type === 'view'
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const obj = await UserTagApi.getTag(id)
      formData.value = handleFormData(obj, false)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  formLoading.value = true
  try {
    const data = handleFormData(formData.value, true)
    if (formType.value === 'create') {
      await UserTagApi.createTag(data)
      message.success('新增成功')
    } else {
      await UserTagApi.updateTag(data)
      message.success('修改成功')
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
// 处理表单数据
const handleFormData = (obj, toForm = true) => {
  if (toForm) {
    return {
      accountTagId: obj.accountTagId,
      tagName: obj.tagName,
      gender: obj.gender,
      memberLevel: obj.memberLevel?.join(',') || null,
      region: obj.regionType === 0 ? null : (obj.region || []).join(','),
      registerStart: obj.registerTime === 0 ? null : obj.registerStart, // 注册开始时间
      registerEnd: obj.registerTime === 0 ? null : obj.registerEnd, // 注册结束时间
      registerDays: obj.registerTime === 0 ? null : obj.registerDays, // 注册天数大于
      orderCount: obj.orderCountCondition === 0 ? null : obj.orderCount, // 成交笔数大于
      orderCountStartTime: obj.orderCountCondition === 0 ? null : obj.orderCountStartTime, // 成交笔数统计开始时间
      orderCountEndTime: obj.orderCountCondition === 0 ? null : obj.orderCountEndTime, // 成交笔数统计结束时间
      orderAmount: obj.orderAmountCondition === 0 ? null : obj.orderAmount, // 成交金额大于
      orderAmountStartTime: obj.orderAmountCondition === 0 ? null : obj.orderAmountStartTime, // 成交金额统计开始时间
      orderAmountEndTime: obj.orderAmountCondition === 0 ? null : obj.orderAmountEndTime // 成交金额统计结束时间
    }
  } else {
    return {
      ...obj,
      memberLevel: obj.memberLevel ? obj.memberLevel.split(',').map(Number) : undefined,
      regionType: obj.region ? 1: 0,
      region: obj.region ? obj.region.split(',').map(Number) : null,
      registerTime: obj.registerDays === null ? 0 : 1,
      orderCountCondition: obj.orderCount === null ? 0 : 1,
      orderAmountCondition: obj.orderAmount === null ? 0 : 1
    }
  }
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    accountTagId: undefined, // 用户标签ID
    tagName: undefined, // 标签名称
    gender: 0, // 性别
    memberLevel: [], // 会员等级
    regionType: 0, // 地区条件类型。0：全部地区；1：指定地区
    region: undefined, // 地区
    registerTime: 0, // 注册时间
    registerStart: null, // 注册开始时间
    registerEnd: null, // 注册结束时间
    registerDays: null, // 注册天数大于
    orderCountCondition: 0, // 成交笔数条件
    orderCount: null, // 成交笔数大于
    orderCountStartTime: null, // 成交笔数统计开始时间
    orderCountEndTime: null, // 成交笔数统计结束时间
    orderAmountCondition: 0, // 成交笔数条件
    orderAmount: null, // 成交金额大于
    orderAmountStartTime: null, // 成交金额统计开始时间
    orderAmountEndTime: null // 成交金额统计结束时间
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.chunk-title {
  padding: 10px 0;
}
</style>
