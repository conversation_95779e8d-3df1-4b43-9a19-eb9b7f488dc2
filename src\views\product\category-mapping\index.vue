<template>
  <div class="category-mapping-page">
    <div class="category-tree">
      <!-- 查询表单 -->
      <el-card>
        <el-form :model="queryForm" inline>
          <el-form-item label="目标平台" class="!mb-0">
            <el-select
              style="width: 200px"
              v-model="queryForm.targetPlatform"
              placeholder="请选择目标平台"
              @change="handlePlatformChange"
            >
              <el-option
                v-for="item in platformOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="!mb-0">
            <el-button type="primary" @click="refreshCategoryTrees">刷新类目树</el-button>
            <el-button type="success" @click="openAutoMappingDialog">自动映射</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-row :gutter="20" class="mt-20px">
        <!-- 左侧：目标平台类目树 -->
        <el-col :span="12" class="h-100%">
          <el-card class="h-100%">
            <template #header>
              <div class="card-header">
                <span>目标平台类目树({{ getPlatformLabel(queryForm.targetPlatform) }})</span>
                <el-input
                  v-model="targetTreeFilter"
                  placeholder="输入关键字过滤"
                  class="tree-filter"
                  clearable
                />
              </div>
            </template>
            <div class="tree-container target-tree h-100%" v-loading="targetTreeLoading">
              <el-empty v-if="targetTreeData.length === 0" description="请选择目标平台" />
              <el-tree
                v-else
                ref="targetTreeRef"
                :data="targetTreeData"
                :props="defaultProps"
                node-key="id"
                highlight-current
                check-strictly
                lazy
                :load="loadTargetNode"
                :filter-node-method="filterNode"
                @node-click="handleTargetNodeClick"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 中间：本平台类目树 -->
        <el-col :span="12" class="h-100%">
          <el-card class="h-100%">
            <template #header>
              <div class="card-header">
                <span>本平台类目树(YHT)</span>
                <el-input
                  v-model="sourceTreeFilter"
                  placeholder="输入关键字过滤"
                  class="tree-filter"
                  clearable
                />
              </div>
            </template>
            <div class="tree-container source-tree h-100%" v-loading="sourceTreeLoading">
              <el-tree
                ref="sourceTreeRef"
                :data="sourceTreeData"
                :props="defaultProps"
                node-key="id"
                highlight-current
                check-strictly
                lazy
                :load="loadSourceNode"
                :filter-node-method="filterNode"
                @node-click="handleSourceNodeClick"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div class="category-mapping">
      <el-card v-loading="attributeLoading" class="category-mapping-card">
        <template #header>
          <!--<div class="card-header">-->
          <!--  <span>类目属性映射</span>-->
          <!--</div>-->
          <div class="selected-categories">
            <div class="selected-categories-item">
              <div class="label">目标平台类目:</div>
              <div class="value">{{ selectedTargetCategory?.name }}</div>
            </div>
            <div class="selected-categories-item">
              <div class="label">本平台类目:</div>
              <div class="value">{{ selectedSourceCategory?.name }}</div>
            </div>
          </div>
        </template>
        <div v-if="!selectedSourceCategory || !selectedTargetCategory" class="empty-tip">
          <el-empty description="请先选择两侧的类目" />
        </div>
        <el-tabs v-else v-model="activeTab" addable class="h-100%">
          <template v-if="activeTab === 'jsonView'" #add-icon>
            <div class="json-control-buttons">
              <el-button type="primary" size="small" @click="expandAllNodes">全部展开</el-button>
              <el-button type="primary" size="small" @click="collapseAllNodes">全部收缩</el-button>
              <el-button
                type="warning"
                size="small"
                @click="loadFullJsonData"
                v-if="!isFullJsonLoaded"
                >加载完整数据</el-button
              >
            </div>
          </template>
          <el-tab-pane label="属性对比" name="compare">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="attribute-list-header">本平台属性</div>
                <el-table
                  ref="sourceTableRef"
                  :data="sourceAttributeList"
                  style="width: 100%"
                  highlight-current-row
                  @current-change="handleCurrentChange"
                >
                  <el-table-column prop="propertyName" label="属性名称" show-overflow-tooltip />
                  <el-table-column prop="type" label="属性类型" show-overflow-tooltip />
                  <el-table-column prop="relation" label="" align="center" width="60">
                    <template #default="{ row }">
                      <Icon v-if="row.relation" icon="ep:right" :size="16" />
                      <template v-else></template>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="12">
                <div class="attribute-list-header">目标平台属性</div>
                <el-table
                  ref="targetTableRef"
                  :data="targetAttributeList"
                  style="width: 100%"
                  highlight-current-row
                  @current-change="handleTargetCurrentChange"
                >
                  <el-table-column prop="name" label="属性名称" show-overflow-tooltip />
                  <el-table-column prop="type" label="属性类型" show-overflow-tooltip>
                    <template #default="{ row }">
                      {{ queryForm.targetPlatform === 'XHS' ? XHSTypeEnum[row.type] : row.type }}
                    </template>
                  </el-table-column>
                  <el-table-column align="center" fixed="right" label="操作" width="60">
                    <template #default="scope">
                      <el-button v-hasPermi="['promotion:help:update']" link type="primary" @click="handleMatching(scope.row)">匹配</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="JSON视图" name="jsonView" lazy class="h-100%">
            <el-row :gutter="20" class="h-100%">
              <el-col :span="12" class="h-100%">
                <div class="json-view-header">
                  <span>本平台属性结构&nbsp;&nbsp;</span>
                  <el-tag size="small" type="info" v-if="sourceAttributesRaw.length > 0"
                    >{{ sourceAttributesRaw.length }}个属性</el-tag
                  >
                </div>
                <div class="json-view-container" v-loading="jsonViewLoading">
                  <json-viewer
                    :key="sourceJsonViewKey"
                    :value="limitedSourceAttributes"
                    :expand-depth="expandedSourceDepth"
                    copyable
                    sort
                    ref="sourceJsonViewRef"
                  />
                </div>
              </el-col>
              <el-col :span="12" class="h-100%">
                <div class="json-view-header">
                  <span>目标平台属性结构&nbsp;&nbsp;</span>
                  <el-tag size="small" type="info" v-if="targetAttributesRaw.length > 0"
                    >{{ targetAttributesRaw.length }}个属性</el-tag
                  >
                </div>
                <div class="json-view-container" v-loading="jsonViewLoading">
                  <json-viewer
                    :key="targetJsonViewKey"
                    :value="limitedTargetAttributes"
                    :expand-depth="expandedTargetDepth"
                    copyable
                    sort
                    ref="targetJsonViewRef"
                  />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="映射关系" name="mapping" class="h-100%">
            <el-table
              ref="mappingTableRef"
              :data="attributeMappingList"
              style="width: 100%"
            >
              <el-table-column prop="attributeName" label="本平台属性名称" show-overflow-tooltip />
              <el-table-column prop="platformName" label="目标平台属性名称" show-overflow-tooltip />
              <el-table-column align="center" fixed="right" label="操作" width="60">
                <template #default="scope">
                  <el-button v-hasPermi="['promotion:help:delete']" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
    <el-dialog
      v-model="dialogVisible"
      title="当前选中映射预览"
      width="800px"
      destroy-on-close
      @close="handleClose"
    >
      <div class="json-mapping-preview" v-if="selectedSourceAttr && selectedTargetAttr">
        <div class="mapping-preview-content">
          <div class="source-attr">
            <h4>本平台属性</h4>
            <json-viewer :value="selectedSourceAttr" :expand-depth="1" copyable />
          </div>
          <div class="mapping-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
          <div class="target-attr">
            <h4>目标平台属性</h4>
            <json-viewer :value="selectedTargetAttr" :expand-depth="1" copyable />
          </div>
        </div>
        <div class="mapping-actions">
          <el-button type="primary" size="small" @click="confirmJsonMapping">确认映射</el-button>
          <el-button size="small" @click="clearJsonSelection(false)">清除选择</el-button>
        </div>
      </div>
    </el-dialog>
    <MatchingForm ref="matchingFormRef" @success="handleSelcet" />
    
    <!-- 添加自动映射弹窗 -->
    <el-dialog
      v-model="autoMappingDialogVisible"
      title="属性自动映射"
      width="80%"
      destroy-on-close
      :before-close="handleAutoMappingDialogClose"
    >
      <MappingTask ref="mappingTaskRef" :platform="queryForm.targetPlatform" @mapping-completed="handleMappingCompleted" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import MatchingForm from './MatchingForm.vue'
import MappingTask from './MappingTask.vue'
// 替换json组件导入
import JsonViewer from 'vue-json-viewer'
import {
  deleteAttributeMapping,
  getAttributeMappingList,
  getCategoryAttributes,
  getCategoryMappingByTargetId,
  getCategoryPath,
  getCategoryTopList,
  getCategoryTree,
  saveAttributeMapping
} from '@/api/product/categoryMapping'

// 平台选项
const platformOptions = [
  { label: '抖音', value: 'FXG' },
  { label: '快手', value: 'KWAISHOP' },
  { label: '小红书', value: 'XHS' },
  { label: '视频号', value: 'SPHXD' }
]

// 查询表单
const queryForm = ref({
  targetPlatform: 'KWAISHOP'
})

// 类目树相关
const sourceTreeRef = ref()
const targetTreeRef = ref()
const sourceTreeData = ref<any[]>([])
const targetTreeData = ref<any[]>([])
const sourceTreeFilter = ref('')
const targetTreeFilter = ref('')
const sourceTreeLoading = ref(false)
const targetTreeLoading = ref(false)

// 树节点属性配置
const defaultProps = {
  label: 'name',
  children: 'children',
  isLeaf: (data: any) => data.leaf !== 0 // 只有leaf=0时才是非叶子节点
}

// 选中的类目
const selectedSourceCategory = ref<any>(null)
const selectedTargetCategory = ref<any>(null)

// 属性映射相关
const attributeMappingList = ref<any[]>([])
const sourceAttributeList = ref<any[]>([])
const sourceTableRef = ref()
const targetAttributeList = ref<any[]>([])
const targetTableRef = ref()
const XHSTypeEnum = {
  0: '文本',
  1: '单选',
  2: '多选'
}
const attributeLoading = ref(false)
const activeTab = ref('compare')

// 原始属性数据
const sourceAttributesRaw = ref<any[]>([])
const targetAttributesRaw = ref<any[]>([])

// JSON视图相关
const sourceJsonViewRef = ref()
const targetJsonViewRef = ref()
const selectedSourceAttr = ref<any>(null)
const selectedTargetAttr = ref<any>(null)
const dialogVisible = computed(() => {
  return selectedSourceAttr.value && selectedTargetAttr.value
})
const expandedSourceDepth = ref(1)
const expandedTargetDepth = ref(1)
const sourceJsonViewKey = ref(0)
const targetJsonViewKey = ref(0)
const jsonViewLoading = ref(false)
const isFullJsonLoaded = ref(false)
const maxInitialItems = 20 // 初始最多显示的项目数

// 计算属性：限制显示的属性数量
const limitedSourceAttributes = computed(() => {
  if (isFullJsonLoaded.value || sourceAttributesRaw.value.length <= maxInitialItems) {
    return sourceAttributesRaw.value
  }
  return sourceAttributesRaw.value.slice(0, maxInitialItems)
})

const limitedTargetAttributes = computed(() => {
  if (isFullJsonLoaded.value || targetAttributesRaw.value.length <= maxInitialItems) {
    return targetAttributesRaw.value
  }
  return targetAttributesRaw.value.slice(0, maxInitialItems)
})

// 加载完整JSON数据的方法
const loadFullJsonData = () => {
  jsonViewLoading.value = true
  // 使用setTimeout让UI有时间显示加载状态
  setTimeout(() => {
    isFullJsonLoaded.value = true
    // 强制重新渲染
    sourceJsonViewKey.value += 1
    targetJsonViewKey.value += 1
    jsonViewLoading.value = false
    ElMessage.success('已加载完整数据')
  }, 100)
}

// 监听标签页切换，重置JSON视图状态
watch(activeTab, (newTab) => {
  if (newTab === 'jsonView') {
    // 重置到初始状态
    expandedSourceDepth.value = 1
    expandedTargetDepth.value = 1
    isFullJsonLoaded.value = false
  }
})

// 修改JSON视图处理相关逻辑，异步加载缓解性能问题
const handleAttributesLoaded = () => {
  // 当属性数据加载完成时，如果当前在JSON视图标签，延迟处理以避免卡顿
  if (activeTab.value === 'jsonView') {
    jsonViewLoading.value = true
    // 使用setTimeout减轻主线程负担
    setTimeout(() => {
      jsonViewLoading.value = false
    }, 200)
  }
}

// 监听过滤器
watch(sourceTreeFilter, (val) => {
  sourceTreeRef.value?.filter(val)
})

watch(targetTreeFilter, (val) => {
  targetTreeRef.value?.filter(val)
})

// 过滤节点方法
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.name.includes(value)
}

// 获取平台标签
const getPlatformLabel = (platform: string) => {
  const found = platformOptions.find((item) => item.value === platform)
  return found ? found.label : platform
}

// 平台变更处理
const handlePlatformChange = () => {
  // 清空目标类目树
  targetTreeData.value = []
  // 清空选中的目标类目
  selectedTargetCategory.value = null
  // 清空属性映射列表
  attributeMappingList.value = []

  // 重新获取目标平台类目树
  fetchTargetCategoryTopList()
}

// 刷新类目树
const refreshCategoryTrees = () => {
  fetchSourceCategoryTopList()
  fetchTargetCategoryTopList()
}

// 获取本平台顶级类目列表
const fetchSourceCategoryTopList = async () => {
  sourceTreeLoading.value = true
  try {
    const res = await getCategoryTopList('YHT', 1)
    if (res) {
      sourceTreeData.value = res.map((item: any) => ({
        ...item,
        hasChildren: item.leaf === 0
      }))
    }
  } catch (error: any) {
    console.error('获取本平台顶级类目失败', error)
    ElMessage.error('获取本平台顶级类目失败')
  } finally {
    sourceTreeLoading.value = false
  }
}

// 获取目标平台顶级类目列表
const fetchTargetCategoryTopList = async () => {
  targetTreeLoading.value = true
  try {
    const res = await getCategoryTopList(queryForm.value.targetPlatform, 1)
    if (res) {
      targetTreeData.value = res.map((item: any) => ({
        ...item,
        hasChildren: item.leaf === 0 // leaf=0表示有子节点
      }))
    }
  } catch (error: any) {
    console.error('获取目标平台顶级类目失败', error)
    ElMessage.error('获取目标平台顶级类目失败')
  } finally {
    targetTreeLoading.value = false
  }
}

// 加载本平台子节点
const loadSourceNode = async (node: any, resolve: (data: any[]) => void) => {
  if (node.level === 0) {
    // 根节点，返回顶级类目
    return resolve(sourceTreeData.value)
  }

  try {
    const res = await getCategoryTree('YHT', node.data.id, 1)
    if (res) {
      // 处理子节点数据
      const children = res.map((item: any) => ({
        ...item,
        hasChildren: item.leaf === 0
      }))
      resolve(children)
    } else {
      resolve([])
    }
  } catch (error: any) {
    console.error('加载本平台子节点失败', error)
    ElMessage.error('加载本平台子节点失败')
    resolve([])
  }
}

// 加载目标平台子节点
const loadTargetNode = async (node: any, resolve: (data: any[]) => void) => {
  if (node.level === 0) {
    // 根节点，返回顶级类目
    return resolve(targetTreeData.value)
  }

  try {
    const res = await getCategoryTree(queryForm.value.targetPlatform, node.data.id, 1)
    if (res) {
      // 处理子节点数据
      const children = res.map((item: any) => ({
        ...item,
        hasChildren: item.leaf === 0
      }))
      resolve(children)
    } else {
      resolve([])
    }
  } catch (error: any) {
    console.error('加载目标平台子节点失败', error)
    ElMessage.error('加载目标平台子节点失败')
    resolve([])
  }
}

// 处理目标平台类目点击
const handleTargetNodeClick = async (data: any) => {
  if (!data) {
    selectedTargetCategory.value = null
    attributeMappingList.value = []
    return
  }

  // 检查是否为叶子节点，只有叶子节点才加载属性
  if (data.leaf !== 0) {
    selectedTargetCategory.value = data

    try {
      const mappingRes = await getCategoryMappingByTargetId(
        queryForm.value.targetPlatform,
        selectedTargetCategory.value.id
      )

      if (mappingRes && mappingRes.categoryLeafId) {
        // 先清空当前选中状态
        sourceTreeRef.value?.setCurrentKey(null)
        selectedSourceCategory.value = null

        // 获取类目路径并选中对应节点
        try {
          await selectSourceCategoryById(mappingRes.categoryLeafId)
        } catch (error) {
          console.error('选中本平台类目失败:', error)
          ElMessage.error('选中本平台类目失败，请手动选择对应类目')

          // 加载目标类目属性
          attributeLoading.value = true
          targetAttributeList.value = await fetchCategoryAttributes(
            queryForm.value.targetPlatform,
            selectedTargetCategory.value.id
          )
          targetCurrentRow.value = {}
          attributeLoading.value = false
        }
      } else {
        // 没有找到映射关系，只加载目标类目属性
        try {
          attributeLoading.value = true
          targetAttributeList.value = await fetchCategoryAttributes(
            queryForm.value.targetPlatform,
            selectedTargetCategory.value.id
          )

          // 清空源类目选择和属性映射
          sourceTreeRef.value?.setCurrentKey(null)
          selectedSourceCategory.value = null
          attributeMappingList.value = []
          sourceAttributeList.value = []
          sourceCurrentRow.value = {}
          targetCurrentRow.value = {}

          // 提示用户选择本平台类目
          ElMessage.info('请选择对应的本平台类目进行映射')
        } catch (error: any) {
          console.error('获取目标平台类目属性失败', error)
          ElMessage.error('获取目标平台类目属性失败')
          targetAttributeList.value = []
          targetCurrentRow.value = {}
        } finally {
          attributeLoading.value = false
        }
      }
    } catch (error: any) {
      console.error('获取类目映射关系失败', error)
      ElMessage.error('获取类目映射关系失败')

      // 出错时只加载目标类目属性
      try {
        attributeLoading.value = true
        targetAttributeList.value = await fetchCategoryAttributes(
          queryForm.value.targetPlatform,
          selectedTargetCategory.value.id
        )
        // 清空源类目选择和属性映射
        sourceTreeRef.value?.setCurrentKey(null)
        selectedSourceCategory.value = null
        attributeMappingList.value = []
        sourceAttributeList.value = []
        sourceCurrentRow.value = {}
        targetCurrentRow.value = {}
      } catch (e: any) {
        console.error('获取目标平台类目属性失败', e)
        targetAttributeList.value = []
        targetCurrentRow.value = {}
      } finally {
        attributeLoading.value = false
      }
    }
  } else {
    // 非叶子节点，只保存选中状态，不加载属性
    selectedTargetCategory.value = data

    // 清空属性和映射数据
    attributeMappingList.value = []
    sourceAttributeList.value = []
    targetAttributeList.value = []
    sourceCurrentRow.value = {}
    targetCurrentRow.value = {}

    // 清空源类目选择
    sourceTreeRef.value?.setCurrentKey(null)
    selectedSourceCategory.value = null
  }
}

// 处理本平台类目点击
const handleSourceNodeClick = async (data: any) => {
  if (!data) {
    selectedSourceCategory.value = null
    attributeMappingList.value = []
    return
  }

  // 检查是否为叶子节点，只有叶子节点才加载属性
  if (data.leaf !== 0) {
    selectedSourceCategory.value = data

    // 如果已选择了目标类目，则加载属性映射
    if (selectedTargetCategory.value) {
      await loadAttributeMappingData()
    } else {
      // 只加载源类目属性
      try {
        attributeLoading.value = true
        sourceAttributeList.value = await fetchCategoryAttributes(
          'YHT',
          selectedSourceCategory.value.id
        )
        sourceCurrentRow.value = {}
      } catch (error: any) {
        console.error('获取本平台类目属性失败', error)
        ElMessage.error('获取本平台类目属性失败')
        sourceAttributeList.value = []
        sourceCurrentRow.value = {}
      } finally {
        attributeLoading.value = false
      }
    }
  } else {
    // 非叶子节点，只保存选中状态，不加载属性
    selectedSourceCategory.value = data
  }
}

// 根据ID查找并选中本平台类目
const selectSourceCategoryById = async (categoryId: string) => {
  try {
    // 获取类目的完整路径
    const res = await getCategoryPath('YHT', categoryId)
    if (res) {
      const path = res

      // 确保源树已加载
      if (sourceTreeData.value.length === 0) {
        await fetchSourceCategoryTopList()
      }

      // 逐级加载路径
      for (let i = 0; i < path.length; i++) {
        const nodeId = path[i]

        // 加载当前级别的节点
        if (i === 0) {
          // 顶级节点，直接从sourceTreeData中查找
          const node = sourceTreeData.value.find((n) => n.id === nodeId)
          if (!node) {
            console.error(`在顶级节点中未找到节点ID: ${nodeId}`)
            return
          }

          // 如果不是最后一级，则加载子节点
          if (i < path.length - 1) {
            try {
              // 直接调用API获取子节点
              const childrenData = await getCategoryTree('YHT', nodeId, 1)
              if (childrenData) {
                // 处理子节点数据
                // 更新节点的子节点
                node.children = childrenData.map((item: any) => ({
                  ...item,
                  hasChildren: item.leaf === 0
                }))

                // 确保节点展开
                await nextTick()
                const treeNode = sourceTreeRef.value?.getNode(nodeId)
                if (treeNode) {
                  treeNode.expanded = true
                }
              }
            } catch (error) {
              console.error(`加载节点${nodeId}的子节点时出错:`, error)
            }
          }

          // 如果是最后一级，选中该节点
          if (i === path.length - 1) {
            selectedSourceCategory.value = node

            // 通知树组件更新选中状态
            await nextTick()
            sourceTreeRef.value?.setCurrentKey(nodeId)
          }
        } else {
          // 非顶级节点，需要先找到父节点
          const parentNodeId = path[i - 1]

          const parentTreeNode = sourceTreeRef.value?.getNode(parentNodeId)
          if (!parentTreeNode) {
            console.error(`未找到父节点: ${parentNodeId}`)
            return
          }

          // 确保父节点已展开
          parentTreeNode.expanded = true
          await nextTick()

          // 在父节点的子节点中查找当前节点
          if (!parentTreeNode.childNodes || parentTreeNode.childNodes.length === 0) {
            // 父节点的子节点尚未加载，手动加载
            try {
              const childrenData = await getCategoryTree('YHT', parentNodeId, 1)
              if (childrenData) {
                // 处理子节点数据
                // 更新父节点的子节点
                parentTreeNode.data.children = childrenData.map((item: any) => ({
                  ...item,
                  hasChildren: item.leaf === 0
                }))

                // 重新获取父节点，使其包含子节点
                await nextTick()
                const updatedParentNode = sourceTreeRef.value?.getNode(parentNodeId)

                if (updatedParentNode && updatedParentNode.childNodes) {
                  // 在更新后的父节点中查找当前节点
                  const currentNode = updatedParentNode.childNodes.find(
                    (n: any) => n.data.id === nodeId
                  )

                  if (!currentNode) {
                    console.error(`在父节点${parentNodeId}的子节点中未找到节点ID: ${nodeId}`)
                    return
                  }

                  // 如果是最后一级，选中该节点
                  if (i === path.length - 1) {
                    selectedSourceCategory.value = currentNode.data

                    // 通知树组件更新选中状态
                    await nextTick()
                    sourceTreeRef.value?.setCurrentKey(nodeId)
                    // 确保节点可见
                    nextTick(() => {
                      // 通过 data-key 属性查找 DOM 元素
                      const nodeElement = sourceTreeRef.value.$el.querySelector(
                        `[data-key="${nodeId}"]`
                      )
                      nodeElement.scrollIntoView({
                        behavior: 'smooth', // 平滑滚动
                        block: 'center', // 垂直居中
                        inline: 'nearest' // 水平最近位置
                      })
                    })
                  } else {
                    // 如果不是最后一级，确保节点展开
                    currentNode.expanded = true
                  }
                }
              }
            } catch (error) {
              console.error(`加载父节点${parentNodeId}的子节点时出错:`, error)
              return
            }
          } else {
            // 父节点的子节点已加载，直接查找当前节点
            const currentNode = parentTreeNode.childNodes.find((n: any) => n.data.id === nodeId)

            if (!currentNode) {
              console.error(`在父节点${parentNodeId}的子节点中未找到节点ID: ${nodeId}`)
              return
            }

            // 如果是最后一级，选中该节点
            if (i === path.length - 1) {
              selectedSourceCategory.value = currentNode.data
              // 通知树组件更新选中状态
              await nextTick()
              sourceTreeRef.value?.setCurrentKey(nodeId)

              // 确保节点可见
              nextTick(() => {
                // 通过 data-key 属性查找 DOM 元素
                const nodeElement = sourceTreeRef.value.$el.querySelector(`[data-key="${nodeId}"]`)
                nodeElement.scrollIntoView({
                  behavior: 'smooth', // 平滑滚动
                  block: 'center', // 垂直居中
                  inline: 'nearest' // 水平最近位置
                })
              })
            } else {
              // 如果不是最后一级，确保节点展开
              currentNode.expanded = true
              await nextTick()
            }
          }
        }
      }
      // 加载属性映射数据
      await loadAttributeMappingData()
    } else {
      console.error('获取类目路径失败，返回为空')
      ElMessage.warning('获取类目路径失败')
    }
  } catch (error: any) {
    console.error('获取类目路径失败', error)
    ElMessage.error('获取类目路径失败')
  }
}

// 获取类目属性
const fetchCategoryAttributes = async (platform: string, categoryId: string) => {
  try {
    const res = await getCategoryAttributes(platform, categoryId)
    if (res) {
      return res.attribute
    }
    return []
  } catch (error: any) {
    console.error('获取类目属性失败', error)
    ElMessage.error('获取类目属性失败')
    return []
  }
}

// 修改loadRawAttributeData函数，确保直接使用完整的API响应
const loadRawAttributeData = async () => {
  if (!selectedSourceCategory.value || !selectedTargetCategory.value) return

  try {
    jsonViewLoading.value = true

    // 调用API获取完整的原始数据结构
    // 对于源平台属性
    const sourceRes = await getCategoryAttributes('YHT', selectedSourceCategory.value.id)

    // 将完整响应保存到sourceAttributesRaw，而不只是.attribute字段
    if (sourceRes) {
      // 确保保留完整的原始数据结构
      sourceAttributesRaw.value = sourceRes.attribute
    } else {
      sourceAttributesRaw.value = []
    }

    // 对于目标平台属性
    const targetRes = await getCategoryAttributes(
      queryForm.value.targetPlatform,
      selectedTargetCategory.value.id
    )

    // 将完整响应保存到targetAttributesRaw
    if (targetRes) {
      // 确保保留完整的原始数据结构
      targetAttributesRaw.value = targetRes.attribute
    } else {
      targetAttributesRaw.value = []
    }

    // 延迟一点时间显示结果，减轻渲染压力
    setTimeout(() => {
      jsonViewLoading.value = false
      // 每次加载时重置状态
      isFullJsonLoaded.value = false
    }, 200)
  } catch (error) {
    console.error('获取原始属性数据失败', error)
    ElMessage.error('获取原始属性数据失败')
    jsonViewLoading.value = false
  }
}

// 修改展开/收缩函数
// 展开/收缩JSON视图 - 使用vue-json-viewer方式
const expandAllNodes = () => {
  expandedSourceDepth.value = 10
  expandedTargetDepth.value = 10
  // 通过改变key强制组件重新渲染
  sourceJsonViewKey.value += 1
  targetJsonViewKey.value += 1
  ElMessage.success('JSON视图已全部展开')
}

const collapseAllNodes = () => {
  expandedSourceDepth.value = 1
  expandedTargetDepth.value = 1
  // 通过改变key强制组件重新渲染
  sourceJsonViewKey.value += 1
  targetJsonViewKey.value += 1
  ElMessage.success('JSON视图已全部收缩')
}

// 清除JSON选择
const clearJsonSelection = (flag = false) => {
  if (!flag) {
    ElMessageBox.confirm('确认清除选择的属性映射吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        selectedSourceAttr.value = null
        selectedTargetAttr.value = null
      })
      .catch(() => {})
  } else {
    selectedSourceAttr.value = null
    selectedTargetAttr.value = null
  }
}

// 修改确认映射函数，确保正确处理属性映射
const confirmJsonMapping = () => {
  if (!selectedSourceAttr.value || !selectedTargetAttr.value) {
    ElMessage.warning('请先选择本平台和目标平台的属性')
    return
  }

  // 从选中的JSON对象中提取属性ID等信息
  const sourceAttrId = selectedSourceAttr.value.propertyId
  const sourceAttrName = selectedSourceAttr.value.propertyName
  const targetAttrId = selectedTargetAttr.value.id
  const targetAttrName = selectedTargetAttr.value.name

  if (!sourceAttrId || !targetAttrId) {
    ElMessage.warning('所选属性缺少必要的ID信息，无法建立映射')
    return
  }

  // 创建映射关系
  const mapping = {
    platform: queryForm.value.targetPlatform,
    categoryLeafId: selectedSourceCategory.value.id,
    platformCategoryLeafId: selectedTargetCategory.value.id,
    attributeId: sourceAttrId,
    attributeName: sourceAttrName,
    attributeType: selectedSourceAttr.value.propertyType || '',
    platformAttributeId: targetAttrId,
    platformName: targetAttrName,
    platformAttributeType: selectedTargetAttr.value.type || ''
  }

  // 保存映射关系
  handleSaveMapping(mapping)
}

// 修改loadAttributeMappingData函数，为JSON视图添加条件加载
const loadAttributeMappingData = async () => {
  if (!selectedSourceCategory.value || !selectedTargetCategory.value) {
    console.log('未选择源类目或目标类目，无法加载属性映射数据')
    return
  }

  try {
    attributeLoading.value = true

    // 加载源平台类目属性
    const sourceArr = await fetchCategoryAttributes('YHT', selectedSourceCategory.value.id)

    // 加载目标平台类目属性
    let targetArr = await fetchCategoryAttributes(
      queryForm.value.targetPlatform,
      selectedTargetCategory.value.id
    )
    let targetIdKey = ''
    let targetNameKey = ''
    let targetTypeKey = ''
    switch (queryForm.value.targetPlatform) {
      case 'KWAISHOP':
        targetIdKey = 'propId'
        targetNameKey = 'propName'
        targetTypeKey = 'propInputType'
        break
      case 'XHS':
        targetIdKey = 'id'
        targetNameKey = 'name'
        targetTypeKey = 'inputType'
        break
      case 'FXG':
        targetIdKey = 'propertyId'
        targetNameKey = 'propertyName'
        targetTypeKey = 'type'
        break
      case 'SPHXD':
        targetIdKey = 'propId'
        targetNameKey = 'name'
        targetTypeKey = 'type'
        break
    }
    targetArr = targetArr.map((el) => {
      return {
        ...el,
        id: el[targetIdKey],
        name: el[targetNameKey],
        type: el[targetTypeKey]
      }
    })
    // 如果当前标签页是JSON视图，才加载原始属性数据
    if (activeTab.value === 'jsonView') {
      await loadRawAttributeData()
    }

    // 加载属性映射关系
    const mappingRes = await getAttributeMappingList(
      queryForm.value.targetPlatform,
      selectedSourceCategory.value.id,
      selectedTargetCategory.value.id,
    )

    if (mappingRes) {
      const targetObj = {}
      targetArr.forEach((item) => {
        targetObj[item.id] = item
      })
      const sourceObj = {}
      sourceArr.forEach((item) => {
        item.relation = false
        sourceObj[item.propertyId] = item
      })
      const targetMappingList: Array<object> = []
      const noTargetMappingList: Array<object> = []
      const sourceMappingList: Array<object> = []
      const noSourceMappingList: Array<object> = []
      const sourceMappingArr: Array<number> = []
      const targetMappingArr: Array<number> = []
      // 处理属性映射数据
      attributeMappingList.value = mappingRes.map((el) => {
        sourceMappingArr.push(el.attributeId.toString())
        targetMappingArr.push(el.platformAttributeId.toString())
        if (targetObj[el.platformAttributeId] && sourceObj[el.attributeId]) {
          targetMappingList.push(targetObj[el.platformAttributeId])
          sourceMappingList.push({ ...sourceObj[el.attributeId], relation: true })
        } else {
          if (targetObj[el.platformAttributeId]) {
            noTargetMappingList.push(targetObj[el.platformAttributeId])
          }
          if (sourceObj[el.attributeId]) {
            noSourceMappingList.push(sourceObj[el.attributeId])
          }
        }
        return {
          ...el,
          mappingId: el.mappingId || null
        }
      })
      const arr = sourceArr.filter((el) => !sourceMappingArr.includes(el.propertyId.toString()))
      sourceAttributeList.value = [...sourceMappingList, ...noSourceMappingList, ...arr]
      const arr2 = targetArr.filter((el) => !targetMappingArr.includes(el.id.toString()))
      targetAttributeList.value = [...targetMappingList, ...noTargetMappingList, ...arr2]
    } else {
      sourceAttributeList.value = [...sourceArr]
      targetAttributeList.value = [...targetArr]
      // 如果没有映射关系，构建初始映射列表
      attributeMappingList.value = sourceAttributeList.value.map((sourceAttr) => ({
        sourceAttributeId: sourceAttr.id,
        sourceAttributeName: sourceAttr.name,
        sourceAttributeType: sourceAttr.type,
        targetAttributeId: null,
        targetAttributeName: '',
        targetAttributeType: '',
        mappingId: null
      }))
    }
    sourceCurrentRow.value = {}
    targetCurrentRow.value = {}

    // 调用新增的处理函数
    handleAttributesLoaded()
  } catch (error: any) {
    console.error('加载属性映射数据失败', error)
    ElMessage.error('加载属性映射数据失败')
    attributeMappingList.value = []
  } finally {
    attributeLoading.value = false
  }
}
const sourceCurrentRow = ref({})
const handleCurrentChange = (val: object) => {
  selectedSourceAttr.value = null
  nextTick(() => {
    selectedSourceAttr.value = val
  })
}
const targetCurrentRow = ref({})
const handleTargetCurrentChange = (val: object) => {
  selectedTargetAttr.value = null
  nextTick(() => {
    selectedTargetAttr.value = val
  })
}

const handleClose = () => {
  sourceTableRef.value?.setCurrentRow()
  targetTableRef.value?.setCurrentRow()
}

// 修改标签页切换监听，在切换到JSON视图时加载数据
watch(activeTab, async (newTab) => {
  if (newTab === 'jsonView') {
    // 重置到初始状态
    expandedSourceDepth.value = 1
    expandedTargetDepth.value = 1
    isFullJsonLoaded.value = false
    sourceJsonViewKey.value += 1
    targetJsonViewKey.value += 1

    // 如果已经选择了类目，但还没有加载JSON数据，就加载数据
    if (selectedSourceCategory.value && selectedTargetCategory.value) {
      jsonViewLoading.value = true
      await loadRawAttributeData()
      jsonViewLoading.value = false
    }
  }
})

// 保存属性映射
const handleSaveMapping = async (row: any) => {
  if (!row.platformAttributeId) {
    ElMessage.warning('请选择目标平台属性')
    return
  }

  try {
    const mappingData = {
      // id: "string",
      platform: row.platform,
      categoryLeafId: row.categoryLeafId,
      platformCategoryLeafId: row.platformCategoryLeafId,
      attributeId: row.attributeId,
      attributeName: row.attributeName,
      platformAttributeId: row.platformAttributeId,
      platformName: row.platformName
    }
    await saveAttributeMapping(mappingData)
    ElMessage.success('保存属性映射成功')
    // 清除选择
    clearJsonSelection(true)
    // 重新加载属性映射数据
    await loadAttributeMappingData()
  } catch (error: any) {
    console.error('保存属性映射失败', error)
    ElMessage.error('保存属性映射失败')
  }
}


/** 关联商品*/
const matchingFormRef = ref()
const handleMatching = (row) => {
  const candidates = sourceAttributeList.value.filter(el => [null, queryForm.value.targetPlatform].includes(el.sourcePlatform)).map(el => el.propertyName)
  matchingFormRef.value.open(row.name, candidates)
}
const handleSelcet = (val) => {
  sourceAttributeList.value.forEach(el => {
    if (val === el.propertyName) {
      sourceTableRef.value?.setCurrentRow(el)
    }
  })
}

const handleDelete = (id: string) => {
  console.log(1203)
  ElMessageBox.confirm('确认删除此属性映射吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    deleteAttributeMapping(id).then(async () => {
      ElMessage.success('删除成功')
      await loadAttributeMappingData()
    })
  }).catch(() => {})
}
// 初始化
onMounted(() => {
  fetchSourceCategoryTopList()
  fetchTargetCategoryTopList()
})

// 自动映射弹窗相关
const autoMappingDialogVisible = ref(false)
const mappingTaskRef = ref()

// 打开自动映射弹窗
const openAutoMappingDialog = () => {
  autoMappingDialogVisible.value = true
}

// 处理自动映射弹窗关闭
const handleAutoMappingDialogClose = () => {
  ElMessageBox.confirm('确认关闭自动映射窗口？关闭后任务将继续在后台运行', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    autoMappingDialogVisible.value = false
  }).catch(() => {})
}

// 处理映射完成事件
const handleMappingCompleted = () => {
  ElMessage.success('属性映射已完成，正在刷新数据')
  // 重新加载属性映射数据
  if (selectedSourceCategory.value && selectedTargetCategory.value) {
    loadAttributeMappingData()
  }
}
</script>

<style lang="scss" scoped>
.category-mapping-page {
  --margin-top: calc(var(--top-tool-height) + var(--tags-view-height));
  height: calc(100vh - var(--margin-top) - var(--app-footer-height) - 20px);
  display: flex;
  .category-tree {
    width: calc(50% - 10px);
    height: 100%;
    :deep(.el-row) {
      height: calc(100% - 74px - 20px);
      .el-card__body {
        height: calc(100% - 70px);
        .tree-container {
          //height: 500px;
          overflow: auto;
          background: #fff;
        }
      }
    }
  }
  .category-mapping {
    margin-left: 20px;
    width: calc(50% - 10px);
    height: 100%;
    :deep(.el-card) {
      &.category-mapping-card {
        height: 100%;
        .el-card__body {
          height: calc(100% - 60px);
          padding-top: 10px;
          //background: aqua;
          .el-tabs {
            //height: calc(100% - 24px);
            .el-tabs__header {
              .el-tabs__new-tab {
                width: 0;
                position: relative;
                margin-left: 0;
                .json-control-buttons {
                  position: absolute;
                  right: 0;
                  //margin-bottom: 15px;
                  text-align: right;
                  width: 265px;
                }
              }
            }
            .el-tabs__content {
              height: calc(100% - 55px);
              overflow-y: auto;
            }
          }
        }
        .selected-categories {
          display: flex;
          gap: 10px;
          .selected-categories-item {
            width: 50%;
            display: flex;
            align-items: center;
          }

          .label {
            font-weight: bold;
            margin-right: 10px;
          }

          .value {
            color: #409eff;
          }
        }
      }
    }
  }
}
.tree-filter {
  width: 180px;
  margin-left: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-tip {
  padding: 50px 0;
}

.empty-data {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.attribute-list-header {
  font-weight: bold;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.operation-bar {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.attribute-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.value-map-title {
  font-weight: bold;
  padding: 10px 0;
  text-align: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
}

.value-map-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.mapping-arrow {
  width: 100%;
  text-align: center;
}

.arrow-icon {
  font-size: 24px;
  color: #409eff;
}

.mapping-list-container {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.mapping-list-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.mapping-actions {
  margin-top: 20px;
  text-align: right;
}

.json-view-header {
  font-weight: bold;
  padding: 10px 0;
  //border-bottom: 1px solid #EBEEF5;
  //margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.json-view-container {
  height: calc(100% - 44px);
  overflow: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.json-mapping-preview {
  //margin-top: 20px;
  //padding: 15px;
  //border: 1px solid #EBEEF5;
  //border-radius: 4px;
  width: 100%;

  .mapping-preview-content {
    display: flex;
    align-items: flex-start;
    height: 100%;
    min-height: 400px;
    gap: 20px;
  }
  .mapping-arrow {
    width: 40px;
  }

  .source-attr,
  .target-attr {
    flex: 1;
    //width: calc(50% - 20px);
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
    height: 100%;
    h4 {
      margin-top: 0;
      color: #409eff;
    }
  }
}

.attr-detail-header {
  font-weight: bold;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}
</style>
