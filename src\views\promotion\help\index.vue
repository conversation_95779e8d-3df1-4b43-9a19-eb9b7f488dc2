<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="文章分类" prop="typeCode">
        <el-select v-model="queryParams.typeCode" class="!w-240px" placeholder="全部" @keyup.enter="handleQuery">
          <el-option v-for="item in categoryList" :key="item.id" :label="item.typeName" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="文章标题" prop="title">
        <el-input v-model="queryParams.title" class="!w-240px" clearable placeholder="请输入文章标题" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="isPublished">
        <el-select v-model="queryParams.isPublished" class="!w-240px" clearable placeholder="请选择状态">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.PUBLISHED_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['promotion:help:create']" plain type="primary" @click="openForm('create')">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :show-overflow-tooltip="true" :stripe="true">
      <el-table-column align="center" label="ID" min-width="180" prop="id" />
      <el-table-column align="center" label="封面" min-width="80" prop="coverImage">
        <template #default="{ row }">
          <el-image :src="row.coverImage" loading="lazy" class="h-30px w-30px" @click="imagePreview(row.coverImage)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="标题" min-width="180" prop="title" />
      <el-table-column align="center" label="分类" min-width="180" prop="typeCode">
        <template #default="scope">
          {{ categoryList.find((item) => item.code === scope.row.typeCode)?.typeName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="文章简介" min-width="250" prop="summary" />
      <el-table-column align="center" label="状态" min-width="100" prop="isPublished">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PUBLISHED_STATUS" :value="scope.row.isPublished" />
        </template>
      </el-table-column>
      <el-table-column :formatter="dateFormatter" align="center" label="发布时间" prop="updated" width="180px" />
      <el-table-column align="center" fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button v-hasPermi="['promotion:help:update']" link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button v-hasPermi="['promotion:help:delete']" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <HelpForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as HelpApi from '@/api/promotion/help'
import HelpForm from './HelpForm.vue'
import { createImageViewer } from '@/components/ImageViewer'

defineOptions({ name: 'PromotionHelp' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  id: undefined,
  typeCode: undefined,
  title: null,
  isPublished: undefined
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
/** 文章封面预览 */
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        id: queryParams.id || undefined,
        typeCode: queryParams.typeCode || undefined,
        title: queryParams.title || undefined,
        isPublished: queryParams.isPublished,
        // created: queryParams.created?.length ? queryParams.created : undefined
      }
    }
    const data = await HelpApi.getHelpPage(params)
    list.value = data.pageContents
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await HelpApi.deleteHelp(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const categoryList = ref<HelpApi.HelpCategoryVO[]>([])
onMounted(async () => {
  await getList()
  // 加载分类、商品列表
  categoryList.value = (await HelpApi.getSimpleHelpCategoryList()) as HelpApi.HelpCategoryVO[]
})
</script>
