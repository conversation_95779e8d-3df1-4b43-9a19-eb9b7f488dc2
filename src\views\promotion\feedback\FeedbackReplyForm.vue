<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1000">
    <el-form
      ref="formRef"
      label-position="top"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      :disabled="true"
    >
      <el-form-item label="反馈内容：" prop="content">
        <Editor v-model="formData.content" :readonly="true" height="100px" />
      </el-form-item>
      <el-form-item v-if="formData.images.length" label="反馈图片：" prop="images">
        <UploadImgs v-model="formData.images" :disabled="true" height="80px" width="80px" :limit="5" placeholder="只支持.jpg .png 格式，最多5张" />
      </el-form-item>
      <el-form-item label="回复内容：" prop="content">
        <Editor v-model="formData.replyContent" height="250px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as FeedbackApi from '@/api/promotion/feedback'

defineOptions({ name: 'PromotionFeedbackForm' })

// const { t } = useI18n() // 国际化
// const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('意见回复') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
// const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  images: [],
  content: '',
  replyContent: ''
})
const formRules = reactive({
  replyContent: [{ required: true, message: '回复内容不能为空', trigger: ['blur', 'change'] }],
})
const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  resetForm()
  const obj = await FeedbackApi.getFeedback(id)
  obj.content = obj.content || ''
  obj.replyContent = obj.replyContent || ''
  obj.images = obj.images ? JSON.parse(obj.images) : []
  formData.value = obj
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      id: formData.value.id,
      replyContent: formData.value.replyContent,
    }
    await FeedbackApi.replyFeedback(data)
    // if (formType.value === 'create') {
    //   await FeedbackApi.createFeedback(data)
    //   message.success(t('common.createSuccess'))
    // } else {
    //   await FeedbackApi.updateFeedback(data)
    //   message.success(t('common.updateSuccess'))
    // }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    images: [],
    content: '',
    replyContent: ''
  }
  formRef.value?.resetFields()
}

const categoryList = ref<FeedbackApi.FeedbackCategoryVO[]>([])
onMounted(async () => {
  categoryList.value =
    (await FeedbackApi.getSimpleFeedbackCategoryList()) as FeedbackApi.FeedbackCategoryVO[]
})
</script>
