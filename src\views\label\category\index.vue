<template>
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="关键字" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入参数名称" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" @click="openForm" v-hasPermi="['tag:update']"><Icon icon="ep:connection" class="mr-5px" /> 关联类目</el-button>
        <el-button type="primary" v-hasPermi="['tag:update']" @click="handleCancelRelation"><Icon icon="ep:connection" class="mr-5px" />取消关联</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-tree
      ref="treeRef"
      style="max-width: 600px"
      :data="tableData"
      :props="props"
      show-checkbox
      node-key="categoryId"
      :filter-node-method="filterNode"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CategoryDataForm ref="formRef" @success="fetchSupplyCategory" />
</template>
<script lang="ts" setup>
import * as TagApi from '@/api/tag'
import CategoryDataForm from './CategoryDataForm.vue'

defineOptions({ name: 'TagCategory' })

// const message = useMessage() // 消息弹窗
// const { t } = useI18n() // 国际化
const route = useRoute() // 路由
const queryParams = reactive({
  name: '',
  tagId: route.params.id,
  type: 2
})

const queryFormRef = ref() // 搜索的表单

/** 初始化 **/
onMounted(async () => {
  fetchSupplyCategory()
  // fetchSupplyCategory()
  // await getList()
  // 查询字典（精简)列表
  // dictTypeList.value = await TagApi.getSimpleDictTypeList()
})

// const tableRef = ref(null)
const tableData = ref<{}[]>([])
const treeRef = ref()
const props = {
  label: 'name',
  children: 'children',
}

const fetchSupplyCategory = () => {
  TagApi.categoryTree({ tagId: queryParams.tagId, parentId: 0, platform: 'YHT', }).then(res => {
    tableData.value = res|| []
    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.filter()
      }
    })
  }).catch(err => {
    console.log(err)
  })
}
const filterNode = (value: string, data: Tree) => {
  let flag = data.isTagged
  if (value) {
    flag = data.name.includes(value)
  }
  return flag
}

/** 搜索按钮操作 */
const handleQuery = () => {
  treeRef.value.filter(queryParams.name)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 关联类目*/
const formRef = ref()
const openForm = () => {
  formRef.value.open(queryParams.tagId)
}

const handleCancelRelation = () => {
  const list = treeRef.value!.getCheckedNodes(false, false) || []
  if (!list.length) return
  const relationIds = list.filter(node => node.isTagged && node.leaf).map(el => el.categoryId)
  TagApi.deleteRelation({
    tagId: queryParams.tagId,
    relationIds,
    type: 2
  }).then(() => {
    fetchSupplyCategory()
  }).catch(err => {
    console.log(err)
  })
}

</script>
