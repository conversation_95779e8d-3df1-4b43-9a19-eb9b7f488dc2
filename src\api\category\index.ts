import request from '@/config/axios'

export interface ConfigVO {
  id: ''
  platform: ''
  name: ''
  imageUrl: ''
  parentId: ''
  level: ''
  leaf: ''
  children?: ConfigVO[]
  parent?: ConfigVO[]

}

// 获取顶级类目列表信息
export const categoryTopList = (platform: string) => {
  return request.get({ url: `/v1/platform/product/category/top/list/${platform}` })
}

// 获取类目树信息
export const categoryTree = (config: object) => {
  return request.get({ url: `/v1/platform/product/category/tree/${config.platform}/${config.parentId}` })
}

// 获取类目详情
export const categoryDetail = (data: object) => {
  return request.post({ url: '/v1/platform/product/category/details', data })
}

// 新增类目
export const createCategory = (data: ConfigVO) => {
  return request.post({ url: '/v1/platform/product/category/create', data })
}

// 修改类目
export const updateCategory = (data: ConfigVO) => {
  return request.post({ url: '/v1/platform/product/category/update', data })
}

// 删除类目
export const deleteCategory = (data: object) => {
  return request.post({ url: '/v1/platform/product/category/delete', data })
}

// 更新显示状态（递归更新子类目）
export const updateShowStatusRecursion = (data: {
  parentId: string
  platform: string
  showStatus: number
}) => {
  return request.post({ url: '/v1/platform/product/category/update/show-status/recursion', data })
}

