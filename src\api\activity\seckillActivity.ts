import request from '@/config/axios'
import { Sku, Spu } from '@/api/mall/product/spu'

export interface SeckillActivityVO {
  id?: number
  spuId?: number
  name?: string
  status?: number
  remark?: string
  startTime?: Date
  endTime?: Date
  sort?: number
  configIds?: string
  orderCount?: number
  userCount?: number
  totalPrice?: number
  totalLimitCount?: number
  singleLimitCount?: number
  stock?: number
  totalStock?: number
  seckillPrice?: number
  products?: SeckillProductVO[]
}

// 秒杀活动所需属性
export interface SeckillProductVO {
  skuId: number
  spuId: number
  seckillPrice: number
  stock: number
}

// 秒杀时段 VO
export interface SeckillConfigVO {
  id: number // 编号
  name: string // 秒杀时段名称
  startTime: string // 开始时间点
  endTime: string // 结束时间点
  sliderPicUrls: string[] // 秒杀轮播图
  status: number // 活动状态
}

// 扩展 Sku 配置
export type SkuExtension = Sku & {
  productConfig: SeckillProductVO
}

export interface SpuExtension extends Spu {
  skus: SkuExtension[] // 重写类型
}

/*活动*/

// 分页查询活动列表
export const getSeckillConfigPage = async (data: any) => {
  return await request.post({ url: `/v1/platform/product/activity/page`, data })
}

// 获取活动详情
export const getSeckillConfig = async (id: number) => {
  return await request.get({ url: `/v1/platform/product/activity/get?id=` + id })
}

// 创建活动
export const createSeckillConfig = async (data: SeckillConfigVO) => {
  return await request.post({ url: `/v1/platform/product/activity/create`, data })
}

// 更新活动信息
export const updateSeckillConfig = async (data: SeckillConfigVO) => {
  return await request.put({ url: `/v1/platform/product/activity/update`, data })
}

// 删除活动
export const deleteSeckillConfig = async (id: number) => {
  return await request.delete({ url: `/v1/platform/product/activity/delete?id=` + id })
}


/* 时间段 */
// 创建活动时间段
export const createTimeslot = async (data: object) => {
  return await request.post({ url: '/v1/platform/product/activity/timeslot/create', data })
}

// 分页查询活动时间段列表
export const timeslotPage = async (data: object) => {
  return await request.post({ url: '/v1/platform/product/activity/timeslot/page', data })
}

// 获取活动时间段详情
export const getTimeslotDetail = async (id: number) => {
  return await request.get({ url: `/v1/platform/product/activity/timeslot/get?id=` + id })
}

// 更新活动时间段信息
export const updateTimeslot = async (data: SeckillConfigVO) => {
  return await request.put({ url: `/v1/platform/product/activity/timeslot/update`, data })
}

// 删除活动时间段
export const deleteTimeslot = async (id: number) => {
  return await request.delete({ url: `/v1/platform/product/activity/timeslot/delete?id=` + id })
}

/*商品*/

// 分页查询活动商品列表
export const getActivityProductPage = async (data: object) => {
  return await request.post({ url: '/v1/platform/product/activity/product/page', data })
}

// 创建活动商品
export const createActivityProduct = async (data: object) => {
  return await request.post({ url: '/v1/platform/product/activity/product/create', data })
}
// 获取未关联活动的商品列表
export const unassociatedProduct = async (data: SeckillActivityVO) => {
  return await request.post({ url: '/v1/platform/product/activity/product/products', data })
}

// 更新活动商品信息
export const updateActivityProduct = async (data: SeckillActivityVO) => {
  return await request.put({ url: '/v1/platform/product/activity/product/update', data })
}

// 删除活动商品
export const deleteActivityProduct = async (data: object) => {
  return await request.post({ url: '/v1/platform/product/activity/product/delete', data})
}

// 关闭秒杀活动
export const closeActivityProduct = async (id: number) => {
  return await request.put({ url: '/promotion/seckill-activity/close?id=' + id })
}
