<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :align-center="true" width="70%">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <el-form-item label="广告所在位置" prop="positionId">
        <el-select v-model="formData.positionId" placeholder="请选择">
          <el-option
            v-for="item in categoryList"
            :key="item.adPositionId"
            :label="item.name"
            :value="item.adPositionId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="广告编号" prop="code">
        <el-input v-model="formData.code" placeholder="请输入广告编号" />
      </el-form-item>
      <el-form-item label="广告标题" prop="name">
        <el-input v-model="formData.name" placeholder="请输入广告标题" />
      </el-form-item>
      <el-form-item label="广告简介" prop="intro">
        <el-input v-model="formData.intro" type="textarea" placeholder="请输入广告简介" />
      </el-form-item>
      <el-form-item label="跳转链接" prop="linkUrl">
        <el-input v-model="formData.linkUrl" placeholder="请输入跳转链接" />
      </el-form-item>
      <el-form-item label="详情">
        <Editor v-model="formData.detail" height="150px" />
      </el-form-item>
      <el-form-item label="广告封面" prop="coverImages">
        <UploadImgs v-model="formData.coverImageList" height="80px" width="80px" :limit="5" placeholder="只支持.jpg .png 格式，最多5张" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item label="是否上线" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.PUBLISHED_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as AdvertisingApi from '@/api/promotion/advertising'

defineOptions({ name: 'PromotionAdvertisingForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  adPlaceId: undefined, // 广告位ID
  code: "", // 编号
  name: "", // 广告名称
  // publishTime: "", // 发布时间
  // endTime: "", // 结束时间
  positionId: 0, // 广告位置ID
  intro: "", // 简介
  linkUrl: "", // 跳转链接
  detail: "", // 详情
  sort: 0, // 排序
  status: 0, // 状态：0-下架，1-上架
  isOnline: 0, // 是否上线
  coverImages: "", // 封面图(最多5张)
  coverImageList: []
})
const formRules = reactive({
  positionId: [{ required: true, message: '广告所在位置不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '广告编号不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '广告标题不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AdvertisingApi.getAdvertising(id)
      formData.value.coverImageList = JSON.parse(formData.value.coverImages || '[]')
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AdvertisingApi.AdvertisingVO
    data.coverImages = JSON.stringify(data.coverImageList)
    if (formType.value === 'create') {
      await AdvertisingApi.createAdvertising(data)
      message.success(t('common.createSuccess'))
    } else {
      await AdvertisingApi.updateAdvertising(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    adPlaceId: undefined, // 广告位ID
    code: "", // 编号
    name: "", // 广告名称
    // publishTime: "", // 发布时间
    // endTime: "", // 结束时间
    positionId: 0, // 广告位置ID
    intro: "", // 简介
    linkUrl: "", // 跳转链接
    detail: "", // 详情
    sort: 0, // 排序
    status: 0, // 状态：0-下架，1-上架
    isOnline: 0, // 是否上线
    coverImages: "", // 封面图(最多5张)
    coverImageList: []
  }
  formRef.value?.resetFields()
}

const categoryList = ref<AdvertisingApi.AdvertisingCategoryListVO[]>([])
onMounted(async () => {
  categoryList.value = (await AdvertisingApi.getSimpleAdvertisingCategoryList()) as AdvertisingApi.AdvertisingCategoryListVO[]
  console.log(176, categoryList.value)
})
</script>
