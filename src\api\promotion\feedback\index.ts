import request from '@/config/axios'

export interface FeedbackVO {
  id: number
  categoryId: number
  title: string
  author: string
  picUrl: string
  introduction: string
  browseCount: string
  sort: number
  status: number
  spuId: number
  recommendHot: boolean
  recommendBanner: boolean
  content: string
}

export interface ReplyFeedbackVO {
  id: undefined,
  replyContent: undefined
}

export interface FeedbackCategoryVO {
  id: undefined
  name: undefined
  code: undefined
  icon: undefined
  displayFlag: undefined
  sort: undefined
}

// 查询意见反馈管理列表
export const getFeedbackPage = async (data: object) => {
  return await request.post({ url: `/v1/platform/infra/feedback/page`, data })
}

// 查询意见反馈管理详情
export const getFeedback = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/feedback/get?id=` + id })
}

// 新增意见反馈管理
export const replyFeedback = async (data: object) => {
  return await request.post({ url: `/v1/platform/infra/feedback/reply`, data })
}

// 新增意见反馈管理
export const createFeedback = async (data: FeedbackVO) => {
  return await request.post({ url: `/promotion/article/create`, data })
}

// 修改意见反馈管理
export const updateFeedback = async (data: FeedbackVO) => {
  return await request.put({ url: `/promotion/article/update`, data })
}

// 删除意见反馈管理
export const deleteFeedback = async (id: number) => {
  return await request.delete({ url: `/promotion/article/delete?id=` + id })
}


// 分页查询意见反馈类型
export const getFeedbackCategoryPage = async (data: object) => {
  return await request.post({ url: `/v1/platform/infra/feedback-type/page`, data })
}

// 查询意见反馈类型精简信息列表
export const getSimpleFeedbackCategoryList = async () => {
  return await request.get({ url: `/v1/platform/infra/feedback-type/list-visible` })
}

// 查询意见反馈类型详情
export const getFeedbackCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/feedback-type/get?id=` + id })
}

// 查询意见反馈类型详情
export const getFeedbackCategoryVisible = async () => {
  return await request.get({ url: `/v1/platform/infra/feedback-type/list-visible` })
}

// 新增意见反馈类型
export const createFeedbackCategory = async (data: FeedbackCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/feedback-type/create`, data })
}

// 修改意见反馈类型
export const updateFeedbackCategory = async (data: FeedbackCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/feedback-type/update`, data })
}

// 删除意见反馈类型
export const deleteFeedbackCategory = async (id: number) => {
  return await request.post({ url: `/v1/platform/infra/feedback-type/delete?id=` + id })
}
