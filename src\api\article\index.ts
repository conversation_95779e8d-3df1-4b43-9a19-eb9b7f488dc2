import request from '@/config/axios'

export interface ArticleVO {
  id: number
  categoryId: number
  title: string
  author: string
  picUrl: string
  introduction: string
  browseCount: string
  sort: number
  status: number
  spuId: number
  recommendHot: boolean
  recommendBanner: boolean
  content: string
}

export interface ArticleCategoryVO {
  id: number
  typeName: string
  code: string
  picUrl: string
  isVisible: number
  sortOrder: number
  typeImage: string
  description: string
}

// 查询内容列表
export const getArticlePage = async (data: any) => {
  return await request.post({ url: `/v1/platform/infra/content/page`, data })
}

// 查询内容详情
export const getArticle = async (code: string) => {
  return await request.get({ url: `/v1/platform/infra/content/get?code=` + code })
}

// 查询内容详情
export const getArticleDetail = async (id: string) => {
  return await request.get({ url: `/v1/platform/infra/content/get-content?id=${id}` })
}

// 查询内容详情
export const getArticleListOnline = async (code: string) => {
  return await request.get({ url: `/v1/platform/infra/content/list-online?code=` + code })
}

// 创建内容
export const createArticle = async (data: ArticleVO) => {
  return await request.post({ url: `/v1/platform/infra/content/create`, data })
}

// 更新内容
export const updateArticle = async (data: ArticleVO) => {
  return await request.post({ url: `/v1/platform/infra/content/update`, data })
}

// 删除内容
export const deleteArticle = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/content/delete?id=` + id })
}

// 查询内容类型列表
export const getArticleCategoryPage = async (data) => {
  return await request.post({ url: `/v1/platform/infra/content/type/page`, data })
}

// 查询内容类型精简信息列表
export const getSimpleArticleCategoryList = async () => {
  return await request.get({ url: `/v1/platform/infra/content/type/list-visible` })
}

// 查询内容类型详情
export const getArticleCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/content/type/get?id=` + id })
}

// 新增内容类型
export const createArticleCategory = async (data: ArticleCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/content/type/create`, data })
}

// 修改内容类型
export const updateArticleCategory = async (data: ArticleCategoryVO) => {
  return await request.post({ url: `/v1/platform/infra/content/type/update`, data })
}

// 删除内容类型
export const deleteArticleCategory = async (id: number) => {
  return await request.get({ url: `/v1/platform/infra/content/type/delete?id=` + id })
}
