<template>
  <Dialog :title="dialogTitle" :fullscreen="true" v-model="dialogVisible">
    <!--<div class="chunk-title"> 用户信息 </div>-->
    <!--<ContentWrap>-->
      <el-table v-loading="loading" :data="list" :border="true">
        <el-table-column label="用户ID" align="center" prop="userId" width="200" />
        <el-table-column label="用户昵称" align="center" prop="nickname" />
        <el-table-column label="手机号" align="center" prop="mobile" width="150" />
        <el-table-column label="用户等级" align="center" prop="userLevel" width="100" />
        <el-table-column label="消费金额" align="center" prop="consumptionAmount" width="120" />
        <el-table-column label="订单数量" align="center" prop="orderCount" width="120" />
        <el-table-column label="状态" align="center" prop="status" width="120">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="注册时间" align="center" prop="registerTime" width="180" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="paginationData.pageTotal"
        v-model:page="paginationData.pageNum"
        v-model:limit="paginationData.pageSize"
        @pagination="getList"
      />
    <!--</ContentWrap>-->
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import * as UserTagApi from '@/api/system/userTag'

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题

const loading = ref(false) // 列表的加载中
const list = ref([])
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const accountTagId = ref()
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        accountTagId: accountTagId.value
      }
    }
    const data = await UserTagApi.accountTagUsers(params)
    list.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  dialogTitle.value = '用户信息'
  accountTagId.value = id
  getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
<style lang="scss" scoped>
.chunk-title {
  padding: 10px 0;
}
</style>
