# 测试环境：只在打包时使用
NODE_ENV=production

VITE_DEV=false

# 请求路径
#VITE_BASE_URL='http://localhost:48080'
#VITE_BASE_URL='http://yb334b99.natappfree.cc'
#VITE_BASE_URL='http://*************:48080'
VITE_BASE_URL='https://test-api-ecommate.yaotown.com'

# 接口地址
VITE_API_URL=''

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist

# 租户开关
VITE_APP_TENANT_ENABLE=false

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true

# 租户开关
VITE_APP_TENANT_ENABLE=false

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'