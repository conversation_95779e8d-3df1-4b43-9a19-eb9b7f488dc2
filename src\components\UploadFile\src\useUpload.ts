import * as FileApi from '@/api/infra/file'
// import CryptoJS from 'crypto-js'
import { UploadRequestOptions } from 'element-plus/es/components/upload/src/upload'
import { ElLoading } from 'element-plus'

/**
 * 获得上传 URL
 */
export const getUploadUrl = (): string => {
  return import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL + '/v1/platform/infra/file/managementUpload?isPrivate=false&tempFolder='
}

export const useUpload = () => {
  // 后端上传地址
  const uploadUrl = getUploadUrl()
  // 重写ElUpload上传方法
  const httpRequest = async (options: UploadRequestOptions) => {
    // 重写 el-upload httpRequest 文件上传成功会走成功的钩子，失败走失败的钩子
    return new Promise((resolve, reject) => {
      const loadingInstance = ElLoading.service({ fullscreen: true })
      FileApi.updateFile({ multipartFile: options.file })
        .then((res) => {
          loadingInstance.close()
          if (res.code === 0) {
            resolve(res)
          } else {
            reject(res)
          }
        })
        .catch((res) => {
          loadingInstance.close()
          reject(res)
        })
    })
  }

  return {
    uploadUrl,
    httpRequest
  }
}
