<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="字典名称" prop="tagName">
        <el-input v-model="formData.tagName" placeholder="请输入字典名称" />
      </el-form-item>
      <!--<el-form-item label="字典类型" prop="tagState">-->
      <!--  <el-input-->
      <!--    v-model="formData.tagState"-->
      <!--    :disabled="typeof formData.id !== 'undefined'"-->
      <!--    placeholder="请输入参数名称"-->
      <!--  />-->
      <!--</el-form-item>-->
      <el-form-item label="状态" prop="tagState">
        <el-radio-group v-model="formData.tagState">
          <el-radio :value="0">上架</el-radio>
          <el-radio :value="1">下架</el-radio>
          <!--<el-radio-->
          <!--  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"-->
          <!--  :key="dict.value"-->
          <!--  :value="dict.value"-->
          <!--&gt;-->
          <!--  {{ dict.label }}-->
          <!--</el-radio>-->
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="tagSort">
        <el-input-number v-model="formData.tagSort" :min="0" clearable controls-position="right" />
      </el-form-item>
      <!--<el-form-item label="备注" prop="remark">-->
      <!--  <el-input v-model="formData.remark" placeholder="请输入内容" type="textarea" />-->
      <!--</el-form-item>-->
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
// import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TagApi from '@/api/tag'
// import { getProductTagDetail } from '@/api/tag/productTag/index'
// import { CommonStatusEnum } from '@/utils/constants'

defineOptions({ name: 'ProductTagForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  tagId: undefined,
  tagName: '',
  tagSort: undefined,
  tagState: 1,
})
const formRules = reactive({
  tagName: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
  tagSort: [{ required: true, message: '字典排序不能为空', trigger: 'blur' }],
  tagState: [{ required: true, message: '状态不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TagApi.getProductTagDetail(id)
      formLoading.value = false
    } catch (e) {
      formLoading.value = false
    } finally {
      console.log(89)
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as TagApi.ProductTagVO
    data.tagId = data.id
    if (formType.value === 'create') {
      await TagApi.createDictType(data)
      message.success(t('common.createSuccess'))
    } else {
      await TagApi.updateDictType(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    tagId: undefined,
    tagName: '',
    tagSort: undefined,
    tagState: 1,
  }
  formRef.value?.resetFields()
}
</script>
