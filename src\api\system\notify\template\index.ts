import request from '@/config/axios'

export interface NotifyTemplateVO {
  id?: string // 消息ID
  title: string // 消息标题
  content: string // 消息内容
  messageType: string // 消息类型
  jumpType: number // 跳转类型：0-富文本，1-内链，2-外链
  jumpPageType?: number // 跳转页面类型：0-自定义，1-活动，2-订单，3-商品，4-优惠劵
  jumpDetail?: string // 跳转富文本--内容
  jumpPage?: string // 跳转页面
  coverImages: [] // 封面图URL，多个以逗号分隔
  pushPosition: [] // 推送位置
  pushMembers: [] // 推送会员
  accountTagIds: [] // 标签ID列表，当tagType为SELECTED时使用
  pushType: number // 推送类型：0-立即推送，1-定时推送
  pushTime: string // 推送时间，当pushType为1时使用
  scheduledTime: string // 定时推送时间，当pushType为1时使用
  isEnabled: number // 是否启用：0-禁用，1-启用
}

export interface NotifySendReqVO {
  userId: number | null
  templateCode: string
  templateParams: Map<String, Object>
}

// 查询站内信模板列表
export const getNotifyTemplatePage = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/message-push/page', data })
}

// 查询站内信模板详情
export const getNotifyTemplate = async (id: number) => {
  return await request.get({ url: '/v1/platform/infra/message-push/detail?id=' + id })
}

// 新增站内信模板
export const createNotifyTemplate = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/message-push/create', data })
}

// 修改站内信模板
export const updateNotifyTemplate = async (data: object) => {
  return await request.post({ url: '/v1/platform/infra/message-push/update', data })
}

// 删除站内信模板
export const deleteNotifyTemplate = async (id: number) => {
  return await request.post({ url: '/v1/platform/infra/message-push/delete?id=' + id })
}

// 发送站内信
export const sendNotify = (id: number) => {
  return request.post({ url: `/v1/platform/infra/message-push/send?id=${id}` })
}
