# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径
#VITE_BASE_URL='https://test-api-ecommate.yaotown.com'
VITE_BASE_URL='http://192.168.10.99:38787'
#VITE_BASE_URL='http://192.168.10.239:38787'

# 接口地址
#VITE_API_URL=''
VITE_API_URL=''

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 租户开关
VITE_APP_TENANT_ENABLE=false

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'