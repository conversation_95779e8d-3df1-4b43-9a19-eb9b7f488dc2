<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px">
      <el-form-item label="版本编码" prop="versionCode">
        <el-input v-model="queryParams.versionCode" class="!w-240px" clearable placeholder="请输入版本编码" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="版本名称" prop="versionName">
        <el-input v-model="queryParams.versionName" class="!w-240px" clearable placeholder="请输入版本名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['supplier:version:create']" plain type="primary" @click="openForm('create')">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :show-overflow-tooltip="true" :stripe="true">
      <el-table-column align="center" label="版本ID" prop="id" width="180" />
      <el-table-column align="center" label="版本编码" width="180" prop="versionCode" />
      <el-table-column label="版本名称" prop="versionName" width="240" show-overflow-tooltip />
      <el-table-column label="版本描述" prop="description" min-width="240" show-overflow-tooltip />
      <el-table-column :formatter="dateFormatter" align="center" label="创建时间" prop="created" width="180px" />
      <el-table-column align="center" label="上线状态" prop="status" min-width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PUBLISHED_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="250px" fixed="right">
        <template #default="scope">
          <el-button v-hasPermi="['supplier:version:update']" link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button v-hasPermi="['supplier:version:assign']" link preIcon="ep:basketball" title="菜单权限" type="primary" @click="openAssignMenuForm(scope.row)">菜单权限</el-button>
          <el-button v-hasPermi="['supplier:version:delete']" link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SupplierVersionsForm ref="formRef" @success="getList" />
  <!--菜单权限弹框-->
  <SupplierAssignMenuForm ref="assignMenuFormRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as SupplierApi from '@/api/supplier'
import SupplierVersionsForm from './SupplierVersionsForm.vue'
import SupplierAssignMenuForm from './SupplierAssignMenuForm.vue'

defineOptions({ name: 'SupplierVersions' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const paginationData = ref({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryParams = reactive({
  versionName: undefined,
  versionCode: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.value.pageNum,
      pageSize: paginationData.value.pageSize,
      param: {
        versionName: queryParams.versionName || undefined,
        versionCode: queryParams.versionCode || undefined
      }
    }
    const data = await SupplierApi.supplierVersionPage(params)
    list.value = data.pageContents
    paginationData.value.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  paginationData.value.pageNum = 1
  paginationData.value.pageTotal = 0
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SupplierApi.deleteSupplierVersion(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 菜单权限操作 */
const assignMenuFormRef = ref()
const openAssignMenuForm = async (row: SupplierApi.SupplierVersionVO) => {
  assignMenuFormRef.value.open(row)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
