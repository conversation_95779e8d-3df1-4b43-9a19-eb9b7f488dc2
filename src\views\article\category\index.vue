<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="编码" prop="code">
        <el-input
          v-model="queryParams.code"
          class="!w-240px"
          clearable
          placeholder="请输入code"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型名称" prop="typeName">
        <el-input
          v-model="queryParams.typeName"
          class="!w-240px"
          clearable
          placeholder="请输入类型名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否显示" prop="isVisible">
        <el-select v-model="queryParams.isVisible" class="!w-120px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.VISIBLE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          v-hasPermi="['article:category:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :show-overflow-tooltip="true" :stripe="true">
      <el-table-column align="center" label="id" prop="id" min-width="150" />
      <el-table-column align="center" label="编号" min-width="180" prop="code" />
      <el-table-column align="center" label="类型名称" prop="typeName" min-width="240" />
      <el-table-column align="center" label="是否显示" prop="isVisible" min-width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISIBLE_STATUS" :value="scope.row.isVisible" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="排序" prop="sortOrder" min-width="150" />
      <el-table-column :formatter="dateFormatter" align="center" label="创建时间" prop="created" width="180px" />
      <el-table-column align="center" label="操作" width="180px">
        <template #default="scope">
          <el-button v-hasPermi="['article:category:update']" link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button v-hasPermi="['article:category:delete']" link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ArticleCategoryForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as ArticleApi from '@/api/article'
import ArticleCategoryForm from './ArticleCategoryForm.vue'

defineOptions({ name: 'PromotionArticleCategory' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const paginationData = ref({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryParams = reactive({
  typeName: undefined,
  code: undefined,
  isVisible: undefined
})
const queryFormRef = ref() // 搜索的表单
// const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.value.pageNum,
      pageSize: paginationData.value.pageSize,
      param: {
        typeName: queryParams.typeName || undefined,
        code: queryParams.code || undefined,
        isVisible: queryParams.isVisible
      }
    }
    const data = await ArticleApi.getArticleCategoryPage(params)
    list.value = data.pageContents
    paginationData.value.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  paginationData.value.pageNum = 1
  paginationData.value.pageTotal = 0
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ArticleApi.deleteArticleCategory(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
