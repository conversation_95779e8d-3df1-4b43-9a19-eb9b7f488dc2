<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :align-center="true" width="1000">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :disabled="true"
      label-width="110px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="code" prop="code">{{ formData.code }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否回复" prop="replyFlag">
            <dict-tag :type="DICT_TYPE.REPLY_STATUS" :value="formData.replyFlag" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="反馈分类" prop="feedbackTypeName">{{ formData.feedbackTypeName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="反馈时间" prop="questionTime">{{ formData.questionTime }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="反馈人姓名" prop="userName">{{ formData.userName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="反馈人电话" prop="userPhone">{{ formData.userPhone }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="反馈内容" prop="content">
            <Editor v-model="formData.content" :readonly="true" height="200px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item v-if="formData.images.length" label="反馈图片" prop="images">
            <UploadImgs v-model="formData.images" :disabled="true" height="80px" width="80px" :limit="5" />
          </el-form-item>
        </el-col>
        <div class="divider"></div>
        <el-col :span="12">
          <el-form-item label="处理人姓名" prop="userName">{{ formData.userName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理时间" prop="questionTime">{{ formData.questionTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="反馈内容" prop="replyContent">
            <Editor v-model="formData.replyContent" :readonly="true" height="200px" />
          </el-form-item>
        </el-col>
        <!--<el-form-item label="封面图" prop="picUrl">-->
        <!--  <UploadImg v-model="formData.picUrl" height="80px" />-->
        <!--</el-form-item>-->
        <!--<el-form-item label="简介" prop="introduction">-->
        <!--  <el-input v-model="formData.introduction" type="textarea" placeholder="请输入文章简介" />-->
        <!--</el-form-item>-->
        <!--<el-form-item label="详情">-->
        <!--  <Editor v-model="formData.content" height="150px" />-->
        <!--</el-form-item>-->
      </el-row>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import * as FeedbackApi from '@/api/promotion/feedback'

defineOptions({ name: 'PromotionFeedbackForm' })

// const { t } = useI18n() // 国际化
// const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('意见反馈') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: 0,
  code: "",
  images: [],
  feedbackTypeId: 0,
  feedbackTypeName: "",
  questionTime: "",
  content: "",
  attachments: "",
  userName: "",
  userId: 0,
  userPhone: "",
  replyFlag: 0,
  handlerId: 0,
  handlerName: "",
  replyTime: "",
  replyContent: "",
  created: ""
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const obj = await FeedbackApi.getFeedback(id)
      obj.content = obj.content || ''
      obj.replyContent = obj.replyContent || ''
      obj.images = obj.images ? JSON.parse(obj.images) : []
      formData.value = obj
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: 0,
    code: "",
    images: [],
    feedbackTypeId: 0,
    feedbackTypeName: "",
    questionTime: "",
    content: "",
    attachments: "",
    userName: "",
    userId: 0,
    userPhone: "",
    replyFlag: 0,
    handlerId: 0,
    handlerName: "",
    replyTime: "",
    replyContent: "",
    created: ""
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.divider{
  margin: 10px 0;
  width: 100%;
  height: 1px;
  background: #dcdfe6;
}
</style>
