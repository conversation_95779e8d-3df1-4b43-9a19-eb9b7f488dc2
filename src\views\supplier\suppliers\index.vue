<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="供应商名称" prop="name">
        <el-input v-model="queryParams.name" class="!w-240px" clearable placeholder="请输入供应商名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="供应商标识" prop="code">
        <el-input v-model="queryParams.code" class="!w-240px" clearable placeholder="请输入供应商标识" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" class="!w-240px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="created">
        <el-date-picker
          v-model="queryParams.created"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button v-hasPermi="['supplier:export']" :loading="exportLoading" plain type="success" @click="handleExport">
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="企业ID" prop="id" />
      <el-table-column align="center" label="企业名称" prop="enterpriseName" />
      <el-table-column align="center" label="联系人" prop="contact" />
      <el-table-column align="center" label="电话号码" prop="phone" />
      <el-table-column align="center" label="版本" prop="versionName" />
      <el-table-column align="center" label="状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :formatter="dateFormatter" align="center" label="创建时间" prop="created" width="180" />
      <el-table-column :formatter="dateFormatter" align="center" label="到期时间" prop="expireTime" width="180" />
      <el-table-column :width="160" align="center" label="操作" fixed="right">
        <template #default="scope">
          <el-button v-hasPermi="['supplier:detail']" link type="primary" @click="openForm(scope.row.id)">查看</el-button>
          <el-button v-hasPermi="['supplier:detail']" link type="primary" @click="openConfigForm(scope.row.id)">费率</el-button>
          <el-button v-hasPermi="['supplier:detail']" link type="primary" @click="openVersionsConfigForm(scope.row.id)">版本</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="paginationData.pageSize"
      v-model:page="paginationData.pageNum"
      :total="paginationData.pageTotal"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SupplierForm ref="formRef" @success="getList" />
  <!-- 费率配置弹窗：修改 -->
  <SupplierFeeConfig ref="feeConfigFormRef" />
  <!-- 版本率配置弹窗：修改 -->
  <SupplierVersionsConfig ref="versionsConfigRef" @success="getList" />

</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as SupplierApi from '@/api/supplier'
import SupplierForm from './SupplierForm.vue'
import SupplierFeeConfig from './SupplierFeeConfig.vue'
import SupplierVersionsConfig from './SupplierVersionsConfig.vue'

defineOptions({ name: 'SupplierList' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  code: '',
  name: '',
  status: undefined,
  created: []
})
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询供应商列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        code: queryParams.code || undefined,
        name: queryParams.name || undefined,
        status: queryParams.status || undefined,
        created: queryParams.created?.length ? queryParams.created : undefined
      }
    }
    const data = await SupplierApi.getSupplierPage(params)
    list.value = data.pageContents
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (id: number) => {
  formRef.value.open(id)
}
/** 添加/修改操作 */
const feeConfigFormRef = ref()
const openConfigForm = (id: number) => {
  feeConfigFormRef.value.open(id)
}
/** 添加/修改操作 */
const versionsConfigRef = ref()
const openVersionsConfigForm = (id: number) => {
  versionsConfigRef.value.open(id)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SupplierApi.exportSupplier(queryParams)
    download.excel(data, '供应商列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
