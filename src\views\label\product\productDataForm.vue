<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :align-center="true" custom-class="relation-dialog" width="70%">
    <ContentWrap :bottom="false" :border="false">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="queryParams.productName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />重置</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="handleRelation"><Icon icon="ep:connection" class="mr-5px" />关联</el-button>
        </el-form-item>
      </el-form>
      <el-table stripe border style="width: 100%" height="570" v-loading="loading" :data="tableData" @selection-change="selectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="商品主图" width="150" prop="imgUrl">
          <template #default="{ row }">
            <el-image :src="row.imgUrl" loading="lazy" class="h-30px w-30px" @click="imagePreview(row.imgUrl)" />
          </template>
        </el-table-column>
        <el-table-column label="商品id" align="center" prop="itemId" width="150" />
        <el-table-column label="状态" align="center" prop="saleStatus" width="100">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SALE_STATUS" :value="scope.row.saleStatus" />
          </template>
        </el-table-column>
        <el-table-column label="商品名称" align="left" prop="title" show-overflow-tooltip />
      </el-table>
      <Pagination
        :total="paginationData.pageTotal"
        v-model:page="paginationData.pageNum"
        v-model:limit="paginationData.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
  </Dialog>
</template>
<script lang="ts" setup>

import * as TagApi from '@/api/tag'
import { createImageViewer } from '@/components/ImageViewer'
import { DICT_TYPE } from '@/utils/dict'

defineOptions({ name: 'SystemProductForm' })

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('关联商品') // 弹窗的标题

const queryFormRef = ref() // 表单 Ref
const tableData = ref([])
const tagId = ref('')

const queryParams = reactive({
  tagId: '',
  productName: ''
})

const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})

const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        tagId: queryParams.tagId,
        productName: queryParams.productName
      }
    }
    const data = await TagApi.relatedProducts(params)
    tableData.value = data.pageContents || []
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}
/** 打开弹窗 */
const open = async (id: string) => {
  dialogVisible.value = true
  tagId.value = id
  queryParams.tagId = id
  queryParams.productName = ''
  getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
const handleRelation = () => {
  const relationIds = multipleSelection.value.map(el => el.itemId)
  TagApi.saveRelation({
    tagId: queryParams.tagId,
    relationIds,
    type: 1
  }).then(() => {
    handleQuery()
  }).catch(err => {
    console.log(err)
  })
}

const multipleSelection = ref([])
const selectionChange = (val:[]) => {
  multipleSelection.value = val
}
// 查看图片
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    zIndex: 9999999,
    urlList: [imgUrl]
  })
}
</script>
<style lang="scss">
.divider{
  width: 100%;
  height: 1px;
  margin: 10px 0;
  background: #dcdfe6;
}
.el-overlay {
  .el-overlay-dialog{
    .relation-dialog{
      .el-dialog__body{
        padding: 0 !important;
      }
    }
  }
}
</style>
