import request from '@/config/axios'

export type ProductTagVO = {
  id: number | undefined
  name: string
  type: string
  status: number
  remark: string
  created: Date
  tagId: number | undefined
  tagName: string
  tagSort: number | undefined
  tagState: number | undefined
}
export type ProductVO = {
  id: number | undefined
  sort: number | undefined
  label: string
  value: string
  dictType: string
  status: number
  colorType: string
  cssClass: string
  remark: string
  created: Date
}

// 查询商品标签（精简)列表
export const getSimpleDictTypeList = () => {
  return request.get({ url: '/v1/platform/infra/dict-type/list-all-simple' })
}

// 查询商品标签列表
export const getProductTagPage = (data) => {
  return request.post({ url: '/v1/platform/product/tag-manage/page', data })
}

// 查询商品标签详情
export const getProductTagDetail = (tagId: number | string) => {
  return request.get({ url: '/v1/platform/product/tag-manage/get?tagId=' + tagId })
}

// 新增商品标签
export const createDictType = (data: ProductTagVO) => {
  return request.post({ url: '/v1/platform/product/tag-manage/save', data })
}

// 修改商品标签
export const updateDictType = (data: ProductTagVO) => {
  return request.put({ url: '/v1/platform/product/tag-manage/update', data })
}

// 删除商品标签
export const deleteProductTag = (data) => {
  return request.post({ url: '/v1/platform/product/tag-manage/batch', data })
}
// 导出商品标签类型
export const exportProductTag = (params) => {
  return request.download({ url: '/v1/platform/infra/dict-type/export', params })
}

// 获取未关联到指定标签的商品列表
export const relatedProducts = (data) => {
  return request.post({ url: '/v1/platform/product/tag-manage/products', data })
}

// 获取未关联到指定标签的类目列表
export const relatedCategory = (data) => {
	return request.post({ url: '/v1/platform/product/tag-manage/categories', data })
}

// 分页查询标签关联的商品和类目详细信息
export const getProductPage = (data) => {
  return request.post({ url: '/v1/platform/product/tag-manage/relationsDetailPage', data })
}

// 保存标签关联关系
export const saveRelation = (data: object) => {
  return request.post({ url: '/v1/platform/product/tag-manage/relation', data })
}

// 删除标签关联关系
export const deleteRelation = (data: object) => {
  return request.delete({ url: '/v1/platform/product/tag-manage/relation', data })
}

// 获取顶级类目列表信息
export const getSupplyCategory = (platform) => {
  return request.get({ url: `/v1/platform/product/tag-manage/supply-category/top/list/${platform}` })
}

// 获取带标签标记的类目树
export const categoryTree = (data: object) => {
  return request.post({ url: '/v1/platform/product/tag-manage/tagged-category/tree', data })
}
